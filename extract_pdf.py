#!/usr/bin/env python3
import sys
import os

try:
    import PyPDF2
except ImportError:
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyPDF2"])
        import PyPDF2
    except:
        print("无法安装PyPDF2，尝试使用pdfplumber")
        try:
            import pdfplumber
        except ImportError:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pdfplumber"])
            import pdfplumber

def extract_with_pypdf2(pdf_path):
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page_num, page in enumerate(reader.pages):
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                page_text = page.extract_text()
                if page_text:
                    text += page_text
                text += "\n"
            return text
    except Exception as e:
        print(f"PyPDF2提取失败: {e}")
        return None

def extract_with_pdfplumber(pdf_path):
    try:
        import pdfplumber
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                page_text = page.extract_text()
                if page_text:
                    text += page_text
                text += "\n"
        return text
    except Exception as e:
        print(f"pdfplumber提取失败: {e}")
        return None

if __name__ == "__main__":
    pdf_path = "doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf"
    output_path = "extracted_content.txt"
    
    print(f"正在提取PDF: {pdf_path}")
    
    # 尝试PyPDF2
    text = extract_with_pypdf2(pdf_path)
    
    # 如果PyPDF2失败，尝试pdfplumber
    if not text:
        print("PyPDF2失败，尝试pdfplumber...")
        text = extract_with_pdfplumber(pdf_path)
    
    if text:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"成功提取PDF内容到: {output_path}")
        print(f"提取的内容长度: {len(text)} 字符")
        
        # 显示前500个字符作为预览
        print("\n=== 内容预览 ===")
        print(text[:500])
        if len(text) > 500:
            print("...")
    else:
        print("所有PDF提取方法都失败了")
        sys.exit(1)
