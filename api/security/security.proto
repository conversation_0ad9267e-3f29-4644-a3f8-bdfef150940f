syntax = "proto3";

package security;

option go_package = "./security";

// 基础安全服务定义
service BaseSecurityService {
    rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
    rpc SecurityCall(SecurityCallRequest) returns (SecurityCallResponse);
    rpc SecuritySubscribe(SecuritySubscribeRequest) returns (SecuritySubscribeResponse);
    rpc SecurityUnsubscribe(SecurityUnsubscribeRequest) returns (SecurityUnsubscribeResponse);
    rpc SecurityStreamPush(stream SecurityStreamChannel) returns (stream SecurityStreamChannel);
}

// 服务信息
message ServiceInfo {
    string name = 1;        // 服务名称
    string version = 2;     // 服务版本
    string description = 3; // 服务描述
}

// 服务信息获取请求
message GetServiceInfoRequest {
    string name = 1;    // 安全服务名称，如果为空("")则获取所有服务信息
}

// 服务信息获取响应
message GetServiceInfoResponse {
    repeated ServiceInfo services = 1;
}

// 安全服务调用请求
// name -> 安全服务名称
//  - PVIIDPU：光伏二型分布式电源接入单元安全服务
//  - SEU：智慧能源单元安全服务
//  - XXX
// method -> 调用方法名称（调用方法根据服务不同定义不同，根据服务类型查看接口说明手册）
// params -> 调用参数, JSON 格式（根据调用方法查看接口说明手册）
// 安全服务名称列表
message SecurityCallRequest {
    string name = 1;    // 安全服务名称
    string method = 2;  // 调用方法名称
    string params = 3;  // 调用参数, JSON 格式
}

// 安全服务调用响应
message SecurityCallResponse {
    string result = 1;  // 调用结果, JSON 格式
}


// 安全服务订阅请求
// name -> 安全服务名称（同 SecurityCallRequest）
message SecuritySubscribeRequest {
    string name = 1;    // 安全服务名称
    string topic = 2;   // 订阅主题
}

// 安全服务订阅响应
message SecuritySubscribeResponse {
    string token = 1;   // 订阅令牌，用于后续取消订阅
}

// 安全服务取消订阅请求
message SecurityUnsubscribeRequest {
    string token = 1;   // 订阅令牌
}

// 安全服务取消订阅响应
message SecurityUnsubscribeResponse {}

// 安全服务流式推送
message SecurityStreamChannel {
    string token = 1;   // 订阅令牌
    string data = 2;    // 推送数据, JSON 格式
}