syntax = "proto3";

package security;

option go_package = "./security";

// XXX安全服务定义
service XXXSecurityService {
    rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
    rpc SecurityCall(SecurityCallRequest) returns (SecurityCallResponse);
    rpc SecuritySubscribe(SecuritySubscribeRequest) returns (SecuritySubscribeResponse);
    rpc SecurityUnsubscribe(SecurityUnsubscribeRequest) returns (SecurityUnsubscribeResponse);
    rpc SecurityStreamPush(stream SecurityStreamChannel) returns (stream SecurityStreamChannel);
}

// 服务信息
message ServiceInfo {
    string name = 1;        // 服务名称
    string version = 2;     // 服务版本
    string description = 3; // 服务描述
}

// 服务信息获取请求
message GetServiceInfoRequest {}

// 服务信息获取响应
message GetServiceInfoResponse {
    ServiceInfo info = 1;
}

// 安全服务调用请求
message SecurityCallRequest {
    string method = 1;  // 调用方法名称
    string params = 2;  // 调用参数, JSON 格式
}

// 安全服务调用响应
message SecurityCallResponse {
    string result = 1;  // 调用结果, JSON 格式
}


// 安全服务订阅请求
message SecuritySubscribeRequest {
    string topic = 1;   // 订阅主题
}

// 安全服务订阅响应
message SecuritySubscribeResponse {
    string token = 1;   // 订阅令牌，用于后续取消订阅
}

// 安全服务取消订阅请求
message SecurityUnsubscribeRequest {
    string token = 1;   // 订阅令牌
}

// 安全服务取消订阅响应
message SecurityUnsubscribeResponse {}

// 安全服务流式推送
message SecurityStreamChannel {
    string token = 1;   // 订阅令牌
    string data = 2;    // 推送数据, JSON 格式
}