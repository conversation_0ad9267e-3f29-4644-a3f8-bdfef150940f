syntax = "proto3";

package security;

option go_package = "./security";

// 光伏二型分布式电源接入单元（Photovoltaic Type II Distributed Power Unit），安全服务定义 
service PVIIDPUSecurityService {
    rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
    rpc SecurityCall(SecurityCallRequest) returns (SecurityCallResponse);
    rpc SecuritySubscribe(SecuritySubscribeRequest) returns (SecuritySubscribeResponse);
    rpc SecurityUnsubscribe(SecurityUnsubscribeRequest) returns (SecurityUnsubscribeResponse);
    rpc SecurityStreamPush(stream SecurityStreamChannel) returns (stream SecurityStreamChannel);
}

// 服务信息
message ServiceInfo {
    string version = 2;     // 服务版本
    string description = 3; // 服务描述
}

// 服务信息获取请求
message GetServiceInfoRequest {}

// 服务信息获取响应
message GetServiceInfoResponse {
    ServiceInfo info = 1;
}

// 安全服务调用请求（参考接口说明手册），以下以服务初始化为例
// method -> "init"
// params -> {"Type": "01", "Data": {"IP": "XXX", "Port": "XXX", "KeyPin": "XXX"}}
// 调用方法名称列表
// - Init：服务初始化
// - SessNeg：主站会话协商
// - SessNegVal：主站会话协商验证
// - ReadDataVal：抄读数据验证
// - RepDataVal：上报数据验证
// - RepRetDataEnc：上报数据返回报文加密
// - SecTransEnc: 安全传输加密
// - SecTransDec：安全传输解密
// - SymKeyUpd：对称密钥更新
// - StartTest: 开始测试
message SecurityCallRequest {
    string method = 1;  // 调用方法名称
    string params = 2;  // 调用参数, JSON 格式
}

// 安全服务调用响应（参考接口说明手册）
// result -> {"Type": "01", "Result": {"State":"0", "Message":""}}
message SecurityCallResponse {
    string result = 1;  // 调用结果, JSON 格式
}


// 安全服务订阅请求
// topic -> "test_notify" 测试通知
message SecuritySubscribeRequest {
    string topic = 1;   // 订阅主题
}

// 安全服务订阅响应
message SecuritySubscribeResponse {
    string token = 1;   // 订阅令牌，用于后续取消订阅
}

// 安全服务取消订阅请求
message SecurityUnsubscribeRequest {
    string token = 1;   // 订阅令牌
}

// 安全服务取消订阅响应
message SecurityUnsubscribeResponse {}

// 安全服务流式推送
// data -> 开始安全检测后的通知推送
// 1. 698测试报文收发
//    双向数据推送，
//      SecServices -> TestServices: {"Type":"03", "Data": {"Flag":"02", "Paras": {"ChannelIndex"："XXXX", "ChannelMessage": "XXXX", "Info": "XXXX"}}}
//      TestServices -> SecServices: {"Type":"03", "Result": {"Flag":"02", "State":"0", "Message": "", "OutParas": {"ChannelIndex"："XXXX", "ChannelMessage": "XXXX"}}}
// 2. 控制功率源输出
//      SecServices -> TestServices: {"Type":"03", "Data": {"Flag":"03", "Paras": {"Control": "XXXX"}}}
//      TestServices -> SecServices: {"Type":"03", "Result": {"Flag":"03", "State":"0", "Message": ""}}
// 3. 测试结束通知
//      SecServices -> TestServices: {"Type":"03", "Data": {"Flag":"04", "Paras": {"TestItemNo": "XXXX", "Samples": [{"SampleNo": "XXXX", "ChannelIndex": "XXXX",
//				"TestResult": "XXXX"}, …… {"SampleNo": "XXXX", "ChannelIndex": "XXXX", "TestResult": "XXXX"}]}}}
//      TestServices -> SecServices: {"Type":"03", "Result": {"Flag":"04", "State":"0", "Message": ""}}
message SecurityStreamChannel {
    string token = 1;   // 订阅令牌
    string data = 2;    // 推送数据, JSON 格式
}