/**
 * 检测服务网关
 * 根据请求中的检测服务名称重定向到指定服务
 */
syntax = "proto3";

package detection;

option go_package = "./detection";

// 检测服务网关
// 对客户端/浏览器提供统一的检测服务访问入口
service BaseDetectionService {
    // 获取检测服务信息
    // 根据 name 参数获取对应检测服务信息
    // 参数：
    //  - name: 检测服务名称，如果为空("")则获取所有服务信息
    // 返回：
    //  - services: 检测服务信息列表
    rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);

    // 检测服务初始化
    // 参数：
    //  - name: 检测服务名称
    //  - config: 检测配置, JSON 格式（待定, 不同检测服务约定不同）
    // 返回：
    //  - token: 检测令牌，用于后续访问检测服务
    rpc DetectionInitialize(DetectionInitRequest) returns (DetectionInitResponse);

    // 检测服务清理
    // 存在运行中的检测任务时，根据是否强制清理标志决定是否清理
    // 参数：
    //  - token: 检测令牌
    //  - force: 是否强制清理
    // 返回：
    //  - 无
    rpc DetectionCleanup(DetectionCleanupRequest) returns (DetectionCleanupResponse);

    // 获取检测项
    // 参数：
    //  - token: 检测令牌
    // 返回：
    //  - items: 检测项列表
    rpc DetectionGetItems(DetectionGetItemsRequest) returns (DetectionGetItemsResponse);

    // 启动检测
    // 参数：
    //  - token: 检测令牌
    //  - item_ids: 检测项 ID 集, 如果为空则启动所有检测项
    // 返回：
    //  - 服务端流式响应（检测过程、结果信息通过流式响应返回）
    rpc DetectionStart(DetectionStartRequest) returns (stream DetectionStartStreamResponse);

    // 停止检测
    // 调用此接口停止检测时，检测服务并不会释放资源，可通过 restart 恢复检测工作
    // 若想终止检测不再继续执行，需调用 cleanup 接口释放资源
    // 参数：
    //  - token: 检测令牌
    // 返回：
    //  - 无
    rpc DetectionStop(DetectionStopRequest) returns (DetectionStopResponse);

    // 重新开始检测
    // 调用此接口会恢复调用 stop 接口暂停的检测工作，检测过程/结果信息仍有 start 接口的流式响应返回
    // 如果没有 stop 状态的检测任务，接口异常
    // 参数：
    //  - token: 检测令牌
    // 返回：
    //  - 无
    rpc DetectionRestart(DetectionRestartRequest) returns (DetectionRestartResponse);
}

// 检测过程消息类型
enum MessageType {
    // 检测项开始
    START = 0;
    // 检测项进度
    PROGRESS = 1;
    // 检测项过程消息
    MESSAGE = 2;
    // 检测项成功消息
    SUCCESS = 3;
    // 检测项失败消息
    FAILURE = 4;
}

// 服务基本信息
message ServiceInfo {
    // 服务名称
    // 每个检测服务需求分析时确定，不可重复
    string name = 1;
    // 服务版本
    // 用于服务升级时的兼容性判断
    string version = 2;
    // 服务描述
    // 用于展示给用户，描述服务的功能、用途等信息
    string description = 3;
}

// 检测服务的检测项
message DetectionItem {
    // 检测项ID
    // 每个检测服务需求分析时确定，不可重复
    int32 id = 1;
    // 检测项名称
    // 用于展示给用户，描述检测项内容、功能等信息
    string name = 2; 
    // 检测项描述
    // 用于展示给用户，描述检测项的执行过程、结果等信息
    string description = 3;
}

// 服务信息获取请求
message GetServiceInfoRequest {
    // 检测服务名称
    // PVIIDPU -> 光伏 II 型分布式电源接入单元检测服务
    // SEU -> 智慧能源单元检测服务
    // ...
    string name = 1;    
}

// 服务信息获取响应
message GetServiceInfoResponse {
    repeated ServiceInfo services = 1;
}

// 检测初始化请求
message DetectionInitRequest {
    // 检测服务名称
    string name = 1;
    // 检测配置, JSON 格式
    // 检测服务需求分析时确定
    string config = 2;  
}

// 检测初始化响应
message DetectionInitResponse {
    // 检测令牌，用于后续访问检测服务
    string token = 1;   
}

// 检测清理请求
message DetectionCleanupRequest {
     // 检测令牌
    string token = 1;  
    // 是否强制清理
    // 存在运行中的检测任务时，根据是否强制清理标志决定是否清理
    bool force = 2;
}

// 检测清理响应
message DetectionCleanupResponse {}

// 检测项获取请求
message DetectionGetItemsRequest {
    // 检测令牌
    string token = 1;
}

// 检测项获取响应
message DetectionGetItemsResponse {
    // 检测项列表
    repeated DetectionItem items = 1;
}

// 检测启动请求
message DetectionStartRequest {
    // 检测令牌
    string token = 1;
    // 检测项 ID 集
    repeated int32 item_ids = 2;
}


// 检测启动响应（服务端流式响应）
message DetectionStartStreamResponse {
    // 检测项 ID
    int32 item_id = 1;
    // 消息类型  
    MessageType type = 2;
    // 消息内容
    string message = 3;
}

// 检测停止请求
message DetectionStopRequest {
    // 检测令牌
    string token = 1;
}

// 检测停止响应
message DetectionStopResponse {}

// 检测重新开始请求
message DetectionRestartRequest{
    // 检测令牌
    string token = 1;
}

// 检测重新开始响应
message DetectionRestartResponse{}