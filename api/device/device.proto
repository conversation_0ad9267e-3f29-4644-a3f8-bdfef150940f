syntax = "proto3";

package device;

option go_package = "./device";

// 基础设备服务定义（访问除服务信息外的接口，皆需要在 metadata 中携带初始化 detection 服务返回的 token -> key-authorization, value-token）
service BaseDeviceService {
    rpc GetServiceInfo(GetServiceInfoRequest) returns (GetServiceInfoResponse);
    rpc DeviceInitialize(DeviceInitRequest) returns (DeviceInitResponse);
    rpc DeviceCleanup(DeviceCleanupRequest) returns (DeviceCleanupResponse);
    rpc DeviceModifyCommParam(DeviceModifyCommParamRequest) returns (DeviceModifyCommParamResponse);
    rpc DeviceModifyProtocol(DeviceModifyProtocolRequest) returns (DeviceModifyProtocolResponse);
    rpc DeviceGetData(DeviceGetDataRequest) returns (DeviceGetDataResponse);
    rpc DeviceSetData(DeviceSetDataRequest) returns (DeviceSetDataResponse);
    rpc DeviceComm(DeviceCommRequest) returns (DeviceCommResponse);
    rpc DeviceSubscribe(DeviceSubscribeNotify) returns (stream DeviceSubscribeResponse);
    rpc DeviceUnsubscribe(DeviceUnsubscribeRequest) returns (DeviceUnsubscribeResponse);
}

// 服务信息
message ServiceInfo {
    string name = 1;        // 服务名称
    string version = 2;     // 服务版本
    string description = 3; // 服务描述
}

// 设备信息
message DeviceInfo {
    int32 type = 1;             // 设备类型, 0x01: 电能表, 0x10: 逆变器, 0x11: 充电桩, 0x12: 数据采集器
    int32 position = 2;         // 设备检测位
    int32 serial_num = 3;       // 设备序号，默认为 1，应对一个检测位多个同类型设备情况
    /**
     * 通讯参数，JSON 格式，根据不同的设备类型传递不同的参数
     *需要设备服务约定 json 结构
     * 例：
     * 串口通讯：
     * {"type": 0, "param": "COM1,9600,8,N,1"} OR
     * {"type": 0, "port": "COM1", "baud_rate": 9600, "data_bits": 8, "parity": "N", "stop_bits": 1}
     * 网络通讯：
     * {"type": 1, "param": "***********:8080"} OR
     * {"type": 1, "ip": "***********", "port": 8080}
     */
    string comm = 4;          // 通讯参数，JSON 格式，根据不同的设备类型传递不同的参数，需要设备服务约定 json 结构
    string protocol = 5;        // 设备协议
}

// 设备参数数据
message DeviceData {
    int32 param_type = 1;       // 参数类型
    string value = 2;           // 参数值
}

// 服务信息获取请求
message GetServiceInfoRequest {
    string name = 1;    // 设备服务名称，如果为空("")则获取所有服务信息
}

// 服务信息获取响应
message GetServiceInfoResponse {
    repeated ServiceInfo services = 1;
}

// 设备（模拟）初始化请求
message DeviceInitRequest {
    string token = 1;   // 检测服务令牌，用于后续访问设备服务
    repeated DeviceInfo devices = 2;
}

// 设备初始化响应
message DeviceInitResponse {
    repeated DeviceInfo devices = 1; // 初始化成功的设备信息
    repeated DeviceInfo failed_devices = 2; // 初始化失败的设备信息
}

// 设备清理请求
message DeviceCleanupRequest {
    string token = 1;   // 检测服务令牌，确定设备与检测服务关联关系
    repeated DeviceInfo devices = 2;
}

// 设备清理响应
message DeviceCleanupResponse {
    repeated DeviceInfo devices = 1; // 清理成功的设备信息
    repeated DeviceInfo failed_devices = 2; // 清理失败的设备信息
}

// 设备通信参数修改请求
message DeviceModifyCommParamRequest {
    string token = 1;   // 检测服务令牌，确定设备与检测服务关联关系
    int32 type = 2;             // 设备类型
    int32 position = 3;         // 设备检测位
    int32 serial_num = 4;       // 设备序号
    string comm = 5;          // 通讯参数，JSON 格式，根据不同的设备类型传递不同的参数，需要设备服务约定 json 结构 
}

// 设备通信参数修改响应
message DeviceModifyCommParamResponse {}

// 设备协议修改请求
message DeviceModifyProtocolRequest {
    string token = 1;   // 检测服务令牌，确定设备与检测服务关联关系
    int32 type = 2;             // 设备类型
    int32 position = 3;         // 设备检测位
    int32 serial_num = 4;       // 设备序号
    string protocol = 5;        // 设备协议
}

// 设备协议修改响应
message DeviceModifyProtocolResponse {}

// 设备数据获取请求
message DeviceGetDataRequest {
    string token = 1;   // 检测服务令牌，确定设备与检测服务关联关系
    int32 type = 2;                 // 设备类型
    int32 position = 3;             // 设备检测位
    int32 serial_num = 4;           // 设备序号
    repeated int32 param_type = 5;  // 参数类型
}

// 设备数据获取响应
message DeviceGetDataResponse {
    repeated DeviceData data = 1;   // 获取的设备数据集
}

// 设备数据设置请求
message DeviceSetDataRequest {
    string token = 1;   // 检测服务令牌，确定设备与检测服务关联关系
    int32 type = 2;                 // 设备类型
    int32 position = 3;             // 设备检测位
    int32 serial_num = 4;           // 设备序号
    repeated DeviceData data = 5;   // 设置的设备数据集
}

// 设备数据设置响应
message DeviceSetDataResponse {}

// 设备通讯请求
message DeviceCommRequest {
    string token = 1;   // 检测服务令牌，确定设备与检测服务关联关系
    int32 type = 2;             // 设备类型
    int32 position = 3;         // 设备检测位
    int32 serial_num = 4;       // 设备序号
    string data = 5;            // 通讯数据
}

// 设备通讯响应
message DeviceCommResponse {
    string data = 1;    // 通讯数据
}

// 订阅通知
message DeviceSubscribeNotify {
    string token = 1;   // 订阅令牌（检测服务令牌）
    string topic = 2;    // 订阅主题
}

// 订阅响应
message DeviceSubscribeResponse {
    
}

// 取消订阅请求
message DeviceUnsubscribeRequest {
    string token = 1;   // 订阅令牌（检测服务令牌）
    string topic = 2;    // 订阅主题
}

// 取消订阅响应
message DeviceUnsubscribeResponse {}
