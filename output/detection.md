### API Reference

### Table of Contents


- Services
    - [BaseDetectionService](###detectionbasedetectionservice)
  


- Messages
    - [DetectionCleanupRequest](###detectioncleanuprequest)
    - [DetectionCleanupResponse](###detectioncleanupresponse)
    - [DetectionGetItemsRequest](###detectiongetitemsrequest)
    - [DetectionGetItemsResponse](###detectiongetitemsresponse)
    - [DetectionInitRequest](###detectioninitrequest)
    - [DetectionInitResponse](###detectioninitresponse)
    - [DetectionItem](###detectionitem)
    - [DetectionRestartRequest](###detectionrestartrequest)
    - [DetectionRestartResponse](###detectionrestartresponse)
    - [DetectionStartRequest](###detectionstartrequest)
    - [DetectionStartStreamResponse](###detectionstartstreamresponse)
    - [DetectionStopRequest](###detectionstoprequest)
    - [DetectionStopResponse](###detectionstopresponse)
    - [GetServiceInfoRequest](###getserviceinforequest)
    - [GetServiceInfoResponse](###getserviceinforesponse)
    - [ServiceInfo](###serviceinfo)
  


- Enums
    - [MessageType](###messagetype)
  


- [Scalar Value Types](###scalar-value-types)



### BaseDetectionService {###detectionbasedetectionservice}
检测服务网关
对客户端/浏览器提供统一的检测服务访问入口

#### GetServiceInfo

> **rpc** GetServiceInfo([GetServiceInfoRequest](###getserviceinforequest))
    [GetServiceInfoResponse](###getserviceinforesponse)

获取检测服务信息
根据 name 参数获取对应检测服务信息
参数：
 - name: 检测服务名称，如果为空("")则获取所有服务信息
返回：
 - services: 检测服务信息列表
#### DetectionInitialize

> **rpc** DetectionInitialize([DetectionInitRequest](###detectioninitrequest))
    [DetectionInitResponse](###detectioninitresponse)

检测服务初始化
参数：
 - name: 检测服务名称
 - config: 检测配置, JSON 格式（待定, 不同检测服务约定不同）
返回：
 - token: 检测令牌，用于后续访问检测服务
#### DetectionCleanup

> **rpc** DetectionCleanup([DetectionCleanupRequest](###detectioncleanuprequest))
    [DetectionCleanupResponse](###detectioncleanupresponse)

检测服务清理
存在运行中的检测任务时，根据是否强制清理标志决定是否清理
参数：
 - token: 检测令牌
 - force: 是否强制清理
返回：
 - 无
#### DetectionGetItems

> **rpc** DetectionGetItems([DetectionGetItemsRequest](###detectiongetitemsrequest))
    [DetectionGetItemsResponse](###detectiongetitemsresponse)

获取检测项
参数：
 - token: 检测令牌
返回：
 - items: 检测项列表
#### DetectionStart

> **rpc** DetectionStart([DetectionStartRequest](###detectionstartrequest))
    [DetectionStartStreamResponse](###detectionstartstreamresponse)

启动检测
参数：
 - token: 检测令牌
 - item_ids: 检测项 ID 集, 如果为空则启动所有检测项
返回：
 - 服务端流式响应（检测过程、结果信息通过流式响应返回）
#### DetectionStop

> **rpc** DetectionStop([DetectionStopRequest](###detectionstoprequest))
    [DetectionStopResponse](###detectionstopresponse)

停止检测
调用此接口停止检测时，检测服务并不会释放资源，可通过 restart 恢复检测工作
若想终止检测不再继续执行，需调用 cleanup 接口释放资源
参数：
 - token: 检测令牌
返回：
 - 无
#### DetectionRestart

> **rpc** DetectionRestart([DetectionRestartRequest](###detectionrestartrequest))
    [DetectionRestartResponse](###detectionrestartresponse)

重新开始检测
调用此接口会恢复调用 stop 接口暂停的检测工作，检测过程/结果信息仍有 start 接口的流式响应返回
如果没有 stop 状态的检测任务，接口异常
参数：
 - token: 检测令牌
返回：
 - 无
 <!-- end methods -->
 <!-- end services -->

### Messages


#### DetectionCleanupRequest {###detectioncleanuprequest}
检测清理请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| token | [ string](###string) | 检测令牌 |
| force | [ bool](###bool) | 是否强制清理 存在运行中的检测任务时，根据是否强制清理标志决定是否清理 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionCleanupResponse {###detectioncleanupresponse}
检测清理响应

 <!-- end HasFields -->


#### DetectionGetItemsRequest {###detectiongetitemsrequest}
检测项获取请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| token | [ string](###string) | 检测令牌 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionGetItemsResponse {###detectiongetitemsresponse}
检测项获取响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| items | [repeated DetectionItem](###detectionitem) | 检测项列表 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionInitRequest {###detectioninitrequest}
检测初始化请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 检测服务名称 |
| config | [ string](###string) | 检测配置, JSON 格式 检测服务需求分析时确定 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionInitResponse {###detectioninitresponse}
检测初始化响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| token | [ string](###string) | 检测令牌，用于后续访问检测服务 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionItem {###detectionitem}
检测服务的检测项


| Field | Type | Description |
| ----- | ---- | ----------- |
| id | [ int32](###int32) | 检测项ID 每个检测服务需求分析时确定，不可重复 |
| name | [ string](###string) | 检测项名称 用于展示给用户，描述检测项内容、功能等信息 |
| description | [ string](###string) | 检测项描述 用于展示给用户，描述检测项的执行过程、结果等信息 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionRestartRequest {###detectionrestartrequest}
检测重新开始请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| token | [ string](###string) | 检测令牌 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionRestartResponse {###detectionrestartresponse}
检测重新开始响应

 <!-- end HasFields -->


#### DetectionStartRequest {###detectionstartrequest}
检测启动请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| token | [ string](###string) | 检测令牌 |
| item_ids | [repeated int32](###int32) | 检测项 ID 集 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionStartStreamResponse {###detectionstartstreamresponse}
检测启动响应（服务端流式响应）


| Field | Type | Description |
| ----- | ---- | ----------- |
| item_id | [ int32](###int32) | 检测项 ID |
| type | [ MessageType](###messagetype) | 消息类型 |
| message | [ string](###string) | 消息内容 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionStopRequest {###detectionstoprequest}
检测停止请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| token | [ string](###string) | 检测令牌 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### DetectionStopResponse {###detectionstopresponse}
检测停止响应

 <!-- end HasFields -->


#### GetServiceInfoRequest {###getserviceinforequest}
服务信息获取请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 检测服务名称 PVIIDPU -> 光伏 II 型分布式电源接入单元检测服务 SEU -> 智慧能源单元检测服务 ... |
 <!-- end Fields -->
 <!-- end HasFields -->


#### GetServiceInfoResponse {###getserviceinforesponse}
服务信息获取响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| services | [repeated ServiceInfo](###serviceinfo) | none |
 <!-- end Fields -->
 <!-- end HasFields -->


#### ServiceInfo {###serviceinfo}
服务基本信息


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 服务名称 每个检测服务需求分析时确定，不可重复 |
| version | [ string](###string) | 服务版本 用于服务升级时的兼容性判断 |
| description | [ string](###string) | 服务描述 用于展示给用户，描述服务的功能、用途等信息 |
 <!-- end Fields -->
 <!-- end HasFields -->
 <!-- end messages -->

### Enums


#### MessageType {###messagetype}
检测过程消息类型

| Name | Number | Description |
| ---- | ------ | ----------- |
| START | 0 | 检测项开始 |
| PROGRESS | 1 | 检测项进度 |
| MESSAGE | 2 | 检测项过程消息 |
| SUCCESS | 3 | 检测项成功消息 |
| FAILURE | 4 | 检测项失败消息 |


 <!-- end Enums -->
 <!-- end Files -->

### Scalar Value Types

| .proto Type | Notes | C++ Type | Java Type | Python Type |
| ----------- | ----- | -------- | --------- | ----------- |
| <div><h4 id="double" /></div><a name="double" /> double |  | double | double | float |
| <div><h4 id="float" /></div><a name="float" /> float |  | float | float | float |
| <div><h4 id="int32" /></div><a name="int32" /> int32 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead. | int32 | int | int |
| <div><h4 id="int64" /></div><a name="int64" /> int64 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead. | int64 | long | int/long |
| <div><h4 id="uint32" /></div><a name="uint32" /> uint32 | Uses variable-length encoding. | uint32 | int | int/long |
| <div><h4 id="uint64" /></div><a name="uint64" /> uint64 | Uses variable-length encoding. | uint64 | long | int/long |
| <div><h4 id="sint32" /></div><a name="sint32" /> sint32 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s. | int32 | int | int |
| <div><h4 id="sint64" /></div><a name="sint64" /> sint64 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s. | int64 | long | int/long |
| <div><h4 id="fixed32" /></div><a name="fixed32" /> fixed32 | Always four bytes. More efficient than uint32 if values are often greater than 2^28. | uint32 | int | int |
| <div><h4 id="fixed64" /></div><a name="fixed64" /> fixed64 | Always eight bytes. More efficient than uint64 if values are often greater than 2^56. | uint64 | long | int/long |
| <div><h4 id="sfixed32" /></div><a name="sfixed32" /> sfixed32 | Always four bytes. | int32 | int | int |
| <div><h4 id="sfixed64" /></div><a name="sfixed64" /> sfixed64 | Always eight bytes. | int64 | long | int/long |
| <div><h4 id="bool" /></div><a name="bool" /> bool |  | bool | boolean | boolean |
| <div><h4 id="string" /></div><a name="string" /> string | A string must always contain UTF-8 encoded or 7-bit ASCII text. | string | String | str/unicode |
| <div><h4 id="bytes" /></div><a name="bytes" /> bytes | May contain any arbitrary sequence of bytes. | string | ByteString | str |
