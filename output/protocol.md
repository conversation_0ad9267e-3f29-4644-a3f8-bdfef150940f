### API Reference

### Table of Contents


- Services
    - [BaseProtocolService](###protocolbaseprotocolservice)
  


- Messages
    - [CPDORequest](###cpdorequest)
    - [CPDOResponse](###cpdoresponse)
    - [CPDRequest](###cpdrequest)
    - [CPDResponse](###cpdresponse)
    - [CPORequest](###cporequest)
    - [CPOResponse](###cporesponse)
    - [CPPRequest](###cpprequest)
    - [CPPResponse](###cppresponse)
    - [GetServiceInfoRequest](###getserviceinforequest)
    - [GetServiceInfoResponse](###getserviceinforesponse)
    - [ServiceInfo](###serviceinfo)
  



- [Scalar Value Types](###scalar-value-types)



### BaseProtocolService {###protocolbaseprotocolservice}
通信协议服务网关
对检测服务提供协议报文的组织及解析支持

#### GetServiceInfo

> **rpc** GetServiceInfo([GetServiceInfoRequest](###getserviceinforequest))
    [GetServiceInfoResponse](###getserviceinforesponse)

获取服务信息
参数
  - name: 服务名称，如果为空("")则获取所有服务信息
返回
  - services: 服务信息列表
#### ProtocolDataDomainOrganization

> **rpc** ProtocolDataDomainOrganization([CPDORequest](###cpdorequest))
    [CPDOResponse](###cpdoresponse)

协议数据域组织
参数
  - type: 协议类型
  - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
            例 Modbus 需要寄存器类型、地址、数据等
            例 DL/T 698.45 需要数据结构类型、地址、数据等
返回
  - datadomain: 组织后的数据, 字符串格式
#### ProtocolOrganization

> **rpc** ProtocolOrganization([CPORequest](###cporequest))
    [CPOResponse](###cporesponse)

协议组织
参数
  - type: 协议类型
  - params: 组织参数, JSON 格式（待补充完善，根据具体协议类型）
            例 Modbus 需要寄存器类型、地址、数据等
            例 DL/T 698.45 需要数据结构类型、地址、数据等
返回
  - frame: 组织后的数据, 字符串格式
#### ProtocolParsing

> **rpc** ProtocolParsing([CPPRequest](###cpprequest))
    [CPPResponse](###cppresponse)

协议解析
参数
  - type: 协议类型
  - frame: 待解析的数据, 字符串格式
返回
  - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
#### ProtocolDataDomainParsing

> **rpc** ProtocolDataDomainParsing([CPDRequest](###cpdrequest))
    [CPDResponse](###cpdresponse)

协议数据域解析
参数
  - type: 协议类型
  - datadomain: 待解析的数据, 字符串格式
返回
  - data: 解析后的数据, JSON 格式（待补充，根据具体协议类型）
 <!-- end methods -->
 <!-- end services -->

### Messages


#### CPDORequest {###cpdorequest}
通讯协议数据域组织请求 Communication Protocol DataDomain Organization
DL/T 698.45 协议为例：
{
    "type": 0x0100,      // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
    "addr": "",          // 地址
    "data": { 
        ... // 根据请求类型不同，传递不同的参数
    },
    "timetag": 0        // 时间标签，0 无时间标签（默认无时间标签，可不传）
}


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 协议服务名称, 可用协议类型的 string 格式进行替代 协议类型 -> 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2 |
| params | [ string](###string) | 组织参数, JSON 格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### CPDOResponse {###cpdoresponse}
协议数据域组织响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| datadomain | [ string](###string) | 组织后的数据, 字符串格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### CPDRequest {###cpdrequest}
通讯协议数据域解析请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 协议服务名称, 可用协议类型的 string 格式进行替代 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2 |
| datadomain | [ string](###string) | 待解析的数据, 字符串格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### CPDResponse {###cpdresponse}
协议数据域解析响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| data | [ string](###string) | 解析后的数据, JSON 格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### CPORequest {###cporequest}
通讯协议组织请求 Communication Protocol Organization
DL/T 698.45 协议为例：
1. 先组织的数据域（APDU）
"params": {
    "datadomain": ""
}
2. 传入不同数据直接组织协议报文
"params": {
    "type": 0x0100,  // 数据结构类型 -> 0x0100 登录, 0x0101 心跳, 0x0102 退出登录, 0x0200 建立应用连接, 0x0300 断开应用连接, 0x0501 读取一个对象请求 ...
    "addr": "",      // 地址
    "data": { 
        ... // 根据请求类型不同，传递不同的参数
    },
    "timetag": 0    // 时间标签，0 无时间标签（默认无时间标签，可不传）
}


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 协议服务名称, 可用协议类型的 string 格式进行替代 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2 |
| params | [ string](###string) | 组织参数, JSON 格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### CPOResponse {###cporesponse}
协议组织响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| frame | [ string](###string) | 组织后的数据, 字符串格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### CPPRequest {###cpprequest}
通讯协议解析请求 Communication Protocol Parsing


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 协议服务名称, 可用协议类型的 string 格式进行替代 协议类型 -> 0: Modbus, 1: DL/T 698.45, 2: DL/T 645, 3: Q/GDW 376.1, 4: Q/GDW 1376.2 |
| frame | [ string](###string) | 待解析的数据, 字符串格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### CPPResponse {###cppresponse}
协议解析响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| data | [ string](###string) | 解析后的数据, JSON 格式 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### GetServiceInfoRequest {###getserviceinforequest}
服务信息获取请求


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 协议服务名称，如果为空("")则获取所有服务信息 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### GetServiceInfoResponse {###getserviceinforesponse}
服务信息获取响应


| Field | Type | Description |
| ----- | ---- | ----------- |
| services | [repeated ServiceInfo](###serviceinfo) | 协议服务信息列表 |
 <!-- end Fields -->
 <!-- end HasFields -->


#### ServiceInfo {###serviceinfo}
服务信息


| Field | Type | Description |
| ----- | ---- | ----------- |
| name | [ string](###string) | 协议服务名称，每个协议需求分析时确定，不可重复（可用协议类型的 string 格式进行替代） |
| version | [ string](###string) | 协议版本，用于协议升级时的兼容性判断 |
| description | [ string](###string) | 协议描述，用于展示给用户，描述协议的功能、用途等信息 |
 <!-- end Fields -->
 <!-- end HasFields -->
 <!-- end messages -->

### Enums
 <!-- end Enums -->
 <!-- end Files -->

### Scalar Value Types

| .proto Type | Notes | C++ Type | Java Type | Python Type |
| ----------- | ----- | -------- | --------- | ----------- |
| <div><h4 id="double" /></div><a name="double" /> double |  | double | double | float |
| <div><h4 id="float" /></div><a name="float" /> float |  | float | float | float |
| <div><h4 id="int32" /></div><a name="int32" /> int32 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead. | int32 | int | int |
| <div><h4 id="int64" /></div><a name="int64" /> int64 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead. | int64 | long | int/long |
| <div><h4 id="uint32" /></div><a name="uint32" /> uint32 | Uses variable-length encoding. | uint32 | int | int/long |
| <div><h4 id="uint64" /></div><a name="uint64" /> uint64 | Uses variable-length encoding. | uint64 | long | int/long |
| <div><h4 id="sint32" /></div><a name="sint32" /> sint32 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s. | int32 | int | int |
| <div><h4 id="sint64" /></div><a name="sint64" /> sint64 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s. | int64 | long | int/long |
| <div><h4 id="fixed32" /></div><a name="fixed32" /> fixed32 | Always four bytes. More efficient than uint32 if values are often greater than 2^28. | uint32 | int | int |
| <div><h4 id="fixed64" /></div><a name="fixed64" /> fixed64 | Always eight bytes. More efficient than uint64 if values are often greater than 2^56. | uint64 | long | int/long |
| <div><h4 id="sfixed32" /></div><a name="sfixed32" /> sfixed32 | Always four bytes. | int32 | int | int |
| <div><h4 id="sfixed64" /></div><a name="sfixed64" /> sfixed64 | Always eight bytes. | int64 | long | int/long |
| <div><h4 id="bool" /></div><a name="bool" /> bool |  | bool | boolean | boolean |
| <div><h4 id="string" /></div><a name="string" /> string | A string must always contain UTF-8 encoded or 7-bit ASCII text. | string | String | str/unicode |
| <div><h4 id="bytes" /></div><a name="bytes" /> bytes | May contain any arbitrary sequence of bytes. | string | ByteString | str |
