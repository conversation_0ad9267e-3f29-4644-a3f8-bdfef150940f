<!DOCTYPE html>

<html>
  <head>
    <title>Protocol Documentation</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Ubuntu:400,700,400italic"/>
    <style>
      body {
        width: 60em;
        margin: 1em auto;
        color: #222;
        font-family: "Ubuntu", sans-serif;
        padding-bottom: 4em;
      }

      h1 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      h2 {
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
        margin: 1.5em 0;
      }

      h3 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      a {
        text-decoration: none;
        color: #567e25;
      }

      table {
        width: 100%;
        font-size: 80%;
        border-collapse: collapse;
      }

      thead {
        font-weight: 700;
        background-color: #dcdcdc;
      }

      tbody tr:nth-child(even) {
        background-color: #fbfbfb;
      }

      td {
        border: 1px solid #ccc;
        padding: 0.5ex 2ex;
      }

      td p {
        text-indent: 1em;
        margin: 0;
      }

      td p:nth-child(1) {
        text-indent: 0;  
      }

       
      .field-table td:nth-child(1) {  
        width: 10em;
      }
      .field-table td:nth-child(2) {  
        width: 10em;
      }
      .field-table td:nth-child(3) {  
        width: 6em;
      }
      .field-table td:nth-child(4) {  
        width: auto;
      }

       
      .extension-table td:nth-child(1) {  
        width: 10em;
      }
      .extension-table td:nth-child(2) {  
        width: 10em;
      }
      .extension-table td:nth-child(3) {  
        width: 10em;
      }
      .extension-table td:nth-child(4) {  
        width: 5em;
      }
      .extension-table td:nth-child(5) {  
        width: auto;
      }

       
      .enum-table td:nth-child(1) {  
        width: 10em;
      }
      .enum-table td:nth-child(2) {  
        width: 10em;
      }
      .enum-table td:nth-child(3) {  
        width: auto;
      }

       
      .scalar-value-types-table tr {
        height: 3em;
      }

       
      #toc-container ul {
        list-style-type: none;
        padding-left: 1em;
        line-height: 180%;
        margin: 0;
      }
      #toc > li > a {
        font-weight: bold;
      }

       
      .file-heading {
        width: 100%;
        display: table;
        border-bottom: 1px solid #aaa;
        margin: 4em 0 1.5em 0;
      }
      .file-heading h2 {
        border: none;
        display: table-cell;
      }
      .file-heading a {
        text-align: right;
        display: table-cell;
      }

       
      .badge {
        width: 1.6em;
        height: 1.6em;
        display: inline-block;

        line-height: 1.6em;
        text-align: center;
        font-weight: bold;
        font-size: 60%;

        color: #89ba48;
        background-color: #dff0c8;

        margin: 0.5ex 1em 0.5ex -1em;
        border: 1px solid #fbfbfb;
        border-radius: 1ex;
      }
    </style>

    
    <link rel="stylesheet" type="text/css" href="stylesheet.css"/>
  </head>

  <body>

    <h1 id="title">Protocol Documentation</h1>

    <h2>Table of Contents</h2>

    <div id="toc-container">
      <ul id="toc">
        
          
          <li>
            <a href="#api%2fdetection%2fdetection.proto">api/detection/detection.proto</a>
            <ul>
              
                <li>
                  <a href="#detection.DetectionCleanupRequest"><span class="badge">M</span>DetectionCleanupRequest</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionCleanupResponse"><span class="badge">M</span>DetectionCleanupResponse</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionGetItemsRequest"><span class="badge">M</span>DetectionGetItemsRequest</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionGetItemsResponse"><span class="badge">M</span>DetectionGetItemsResponse</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionInitRequest"><span class="badge">M</span>DetectionInitRequest</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionInitResponse"><span class="badge">M</span>DetectionInitResponse</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionItem"><span class="badge">M</span>DetectionItem</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionRestartRequest"><span class="badge">M</span>DetectionRestartRequest</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionRestartResponse"><span class="badge">M</span>DetectionRestartResponse</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionStartRequest"><span class="badge">M</span>DetectionStartRequest</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionStartStreamResponse"><span class="badge">M</span>DetectionStartStreamResponse</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionStopRequest"><span class="badge">M</span>DetectionStopRequest</a>
                </li>
              
                <li>
                  <a href="#detection.DetectionStopResponse"><span class="badge">M</span>DetectionStopResponse</a>
                </li>
              
                <li>
                  <a href="#detection.GetServiceInfoRequest"><span class="badge">M</span>GetServiceInfoRequest</a>
                </li>
              
                <li>
                  <a href="#detection.GetServiceInfoResponse"><span class="badge">M</span>GetServiceInfoResponse</a>
                </li>
              
                <li>
                  <a href="#detection.ServiceInfo"><span class="badge">M</span>ServiceInfo</a>
                </li>
              
              
              
              
                <li>
                  <a href="#detection.BaseDetectionService"><span class="badge">S</span>BaseDetectionService</a>
                </li>
              
            </ul>
          </li>
        
        <li><a href="#scalar-value-types">Scalar Value Types</a></li>
      </ul>
    </div>

    
      
      <div class="file-heading">
        <h2 id="api/detection/detection.proto">api/detection/detection.proto</h2><a href="#title">Top</a>
      </div>
      <p>检测服务网关</p><p>根据请求中的检测服务名称重定向到指定服务</p>

      
        <h3 id="detection.DetectionCleanupRequest">DetectionCleanupRequest</h3>
        <p>检测清理请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>token</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测令牌 </p></td>
                </tr>
              
                <tr>
                  <td>force</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionCleanupResponse">DetectionCleanupResponse</h3>
        <p>检测清理响应</p>

        

        
      
        <h3 id="detection.DetectionGetItemsRequest">DetectionGetItemsRequest</h3>
        <p>检测项获取请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>token</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测令牌 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionGetItemsResponse">DetectionGetItemsResponse</h3>
        <p>检测项获取响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>items</td>
                  <td><a href="#detection.DetectionItem">DetectionItem</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionInitRequest">DetectionInitRequest</h3>
        <p>检测初始化请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测服务名称 </p></td>
                </tr>
              
                <tr>
                  <td>config</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测配置, JSON 格式（待定, 不同检测服务约定不同） </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionInitResponse">DetectionInitResponse</h3>
        <p>检测初始化响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>token</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测令牌，用于后续访问检测服务 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionItem">DetectionItem</h3>
        <p>检测服务的检测项</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>id</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>检测项ID
每个检测服务需求分析时确定，不可重复 </p></td>
                </tr>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测项名称
用于展示给用户，描述检测项内容、功能等信息 </p></td>
                </tr>
              
                <tr>
                  <td>description</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测项描述
用于展示给用户，描述检测项的执行过程、结果等信息 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionRestartRequest">DetectionRestartRequest</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>token</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionRestartResponse">DetectionRestartResponse</h3>
        <p></p>

        

        
      
        <h3 id="detection.DetectionStartRequest">DetectionStartRequest</h3>
        <p>检测启动请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>token</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测令牌 </p></td>
                </tr>
              
                <tr>
                  <td>item_ids</td>
                  <td><a href="#int32">int32</a></td>
                  <td>repeated</td>
                  <td><p>检测项 ID 集, 如果为空则启动所有检测项 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionStartStreamResponse">DetectionStartStreamResponse</h3>
        <p>检测启动响应（服务端流式响应）</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>item_id</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>检测项 ID </p></td>
                </tr>
              
                <tr>
                  <td>message_type</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>消息类型 -&gt; 0: 检测项开始, 1: 检测项进度, 3: 检测项过程消息, 4: 检测项成功消息, 4: 检测项失败消息 </p></td>
                </tr>
              
                <tr>
                  <td>message</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>消息内容 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionStopRequest">DetectionStopRequest</h3>
        <p>检测停止请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>token</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测令牌 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.DetectionStopResponse">DetectionStopResponse</h3>
        <p>检测停止响应</p>

        

        
      
        <h3 id="detection.GetServiceInfoRequest">GetServiceInfoRequest</h3>
        <p>服务信息获取请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>检测服务名称，如果为空(&#34;&#34;)则获取所有服务信息 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.GetServiceInfoResponse">GetServiceInfoResponse</h3>
        <p>服务信息获取响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>services</td>
                  <td><a href="#detection.ServiceInfo">ServiceInfo</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="detection.ServiceInfo">ServiceInfo</h3>
        <p>服务基本信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>服务名称
每个检测服务需求分析时确定，不可重复 </p></td>
                </tr>
              
                <tr>
                  <td>version</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>服务版本
用于服务升级时的兼容性判断 </p></td>
                </tr>
              
                <tr>
                  <td>description</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>服务描述
用于展示给用户，描述服务的功能、用途等信息 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="detection.BaseDetectionService">BaseDetectionService</h3>
        <p>检测服务网关</p><p>对客户端/浏览器提供统一的检测服务访问入口</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>GetServiceInfo</td>
                <td><a href="#detection.GetServiceInfoRequest">GetServiceInfoRequest</a></td>
                <td><a href="#detection.GetServiceInfoResponse">GetServiceInfoResponse</a></td>
                <td><p>获取检测服务信息
根据 name 参数获取对应检测服务信息
参数：
 - name: 检测服务名称，如果为空(&#34;&#34;)则获取所有服务信息
返回：
 - services: 检测服务信息列表</p></td>
              </tr>
            
              <tr>
                <td>DetectionInitialize</td>
                <td><a href="#detection.DetectionInitRequest">DetectionInitRequest</a></td>
                <td><a href="#detection.DetectionInitResponse">DetectionInitResponse</a></td>
                <td><p>检测服务初始化
参数：
 - name: 检测服务名称
 - config: 检测配置, JSON 格式（待定, 不同检测服务约定不同）
返回：
 - token: 检测令牌，用于后续访问检测服务</p></td>
              </tr>
            
              <tr>
                <td>DetectionCleanup</td>
                <td><a href="#detection.DetectionCleanupRequest">DetectionCleanupRequest</a></td>
                <td><a href="#detection.DetectionCleanupResponse">DetectionCleanupResponse</a></td>
                <td><p>检测服务清理
存在运行中的检测任务时，根据是否强制清理标志决定是否清理
参数：
 - token: 检测令牌
 - force: 是否强制清理
返回：
 - 无</p></td>
              </tr>
            
              <tr>
                <td>DetectionGetItems</td>
                <td><a href="#detection.DetectionGetItemsRequest">DetectionGetItemsRequest</a></td>
                <td><a href="#detection.DetectionGetItemsResponse">DetectionGetItemsResponse</a></td>
                <td><p>获取检测项
参数：
 - token: 检测令牌
返回：
 - items: 检测项列表</p></td>
              </tr>
            
              <tr>
                <td>DetectionStart</td>
                <td><a href="#detection.DetectionStartRequest">DetectionStartRequest</a></td>
                <td><a href="#detection.DetectionStartStreamResponse">DetectionStartStreamResponse</a> stream</td>
                <td><p>启动检测
参数：
 - token: 检测令牌
 - item_ids: 检测项 ID 集, 如果为空则启动所有检测项
返回：
 - 服务端流式响应（检测过程、结果信息通过流式响应返回）</p></td>
              </tr>
            
              <tr>
                <td>DetectionStop</td>
                <td><a href="#detection.DetectionStopRequest">DetectionStopRequest</a></td>
                <td><a href="#detection.DetectionStopResponse">DetectionStopResponse</a></td>
                <td><p>停止检测
调用此接口停止检测时，检测服务并不会释放资源，可通过 restart 恢复检测工作
若想终止检测不再继续执行，需调用 cleanup 接口释放资源
参数：
 - token: 检测令牌
返回：
 - 无</p></td>
              </tr>
            
              <tr>
                <td>DetectionRestart</td>
                <td><a href="#detection.DetectionRestartRequest">DetectionRestartRequest</a></td>
                <td><a href="#detection.DetectionRestartResponse">DetectionRestartResponse</a></td>
                <td><p>重新开始检测
调用此接口会恢复调用 stop 接口暂停的检测工作，检测过程/结果信息仍有 start 接口的流式响应返回
如果没有 stop 状态的检测任务，接口异常
参数：
 - token: 检测令牌
返回：
 - 无</p></td>
              </tr>
            
          </tbody>
        </table>

        
    

    <h2 id="scalar-value-types">Scalar Value Types</h2>
    <table class="scalar-value-types-table">
      <thead>
        <tr><td>.proto Type</td><td>Notes</td><td>C++</td><td>Java</td><td>Python</td><td>Go</td><td>C#</td><td>PHP</td><td>Ruby</td></tr>
      </thead>
      <tbody>
        
          <tr id="double">
            <td>double</td>
            <td></td>
            <td>double</td>
            <td>double</td>
            <td>float</td>
            <td>float64</td>
            <td>double</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="float">
            <td>float</td>
            <td></td>
            <td>float</td>
            <td>float</td>
            <td>float</td>
            <td>float32</td>
            <td>float</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="int32">
            <td>int32</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="int64">
            <td>int64</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="uint32">
            <td>uint32</td>
            <td>Uses variable-length encoding.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int/long</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="uint64">
            <td>uint64</td>
            <td>Uses variable-length encoding.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint32">
            <td>sint32</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint64">
            <td>sint64</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="fixed32">
            <td>fixed32</td>
            <td>Always four bytes. More efficient than uint32 if values are often greater than 2^28.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="fixed64">
            <td>fixed64</td>
            <td>Always eight bytes. More efficient than uint64 if values are often greater than 2^56.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="sfixed32">
            <td>sfixed32</td>
            <td>Always four bytes.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sfixed64">
            <td>sfixed64</td>
            <td>Always eight bytes.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="bool">
            <td>bool</td>
            <td></td>
            <td>bool</td>
            <td>boolean</td>
            <td>boolean</td>
            <td>bool</td>
            <td>bool</td>
            <td>boolean</td>
            <td>TrueClass/FalseClass</td>
          </tr>
        
          <tr id="string">
            <td>string</td>
            <td>A string must always contain UTF-8 encoded or 7-bit ASCII text.</td>
            <td>string</td>
            <td>String</td>
            <td>str/unicode</td>
            <td>string</td>
            <td>string</td>
            <td>string</td>
            <td>String (UTF-8)</td>
          </tr>
        
          <tr id="bytes">
            <td>bytes</td>
            <td>May contain any arbitrary sequence of bytes.</td>
            <td>string</td>
            <td>ByteString</td>
            <td>str</td>
            <td>[]byte</td>
            <td>ByteString</td>
            <td>string</td>
            <td>String (ASCII-8BIT)</td>
          </tr>
        
      </tbody>
    </table>
  </body>
</html>

