package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"

	"tp.service/internal/protocol/dlt69845/datatype"
)

func main() {
	var (
		pdfPath   = flag.String("pdf", "", "PDF文件路径")
		outputDir = flag.String("output", "", "输出目录路径")
		help      = flag.Bool("help", false, "显示帮助信息")
	)

	flag.Parse()

	if *help {
		printHelp()
		return
	}

	// 如果没有指定参数，使用默认值
	if *pdfPath == "" {
		*pdfPath = "doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf"
	}

	if *outputDir == "" {
		*outputDir = "internal/protocol/dlt69845/datatype"
	}

	// 检查PDF文件是否存在
	if _, err := os.Stat(*pdfPath); os.IsNotExist(err) {
		log.Fatalf("PDF文件不存在: %s", *pdfPath)
	}

	// 获取绝对路径
	absPdfPath, err := filepath.Abs(*pdfPath)
	if err != nil {
		log.Fatalf("无法获取PDF文件绝对路径: %v", err)
	}

	absOutputDir, err := filepath.Abs(*outputDir)
	if err != nil {
		log.Fatalf("无法获取输出目录绝对路径: %v", err)
	}

	fmt.Printf("开始提取PDF内容...\n")
	fmt.Printf("PDF文件: %s\n", absPdfPath)
	fmt.Printf("输出目录: %s\n", absOutputDir)

	// 创建PDF提取器 (使用改进版本)
	extractor := datatype.NewPDFExtractorV2(absPdfPath, absOutputDir)

	// 提取内容
	fmt.Println("正在提取PDF内容...")
	if err := extractor.ExtractContent(); err != nil {
		log.Fatalf("提取PDF内容失败: %v", err)
	}

	// 解析A-XDR结构
	fmt.Println("正在解析A-XDR数据结构...")
	if err := extractor.ParseAXDRStructures(); err != nil {
		log.Fatalf("解析A-XDR结构失败: %v", err)
	}

	// 保存到文件
	fmt.Println("正在保存提取的内容...")
	if err := extractor.SaveToFiles(); err != nil {
		log.Fatalf("保存文件失败: %v", err)
	}

	fmt.Println("PDF内容提取完成！")
	fmt.Printf("生成的文件:\n")
	fmt.Printf("- %s/dlt790_6_raw_content_v2.txt (原始内容)\n", absOutputDir)
	fmt.Printf("- %s/axdr_types_v2.go (数据类型定义)\n", absOutputDir)
	fmt.Printf("- %s/axdr_encoding_rules_v2.go (编码规则)\n", absOutputDir)
	fmt.Printf("- %s/axdr_structures_v2.go (结构定义)\n", absOutputDir)

	// 显示提取的内容摘要
	content := extractor.GetContent()
	structuredData := extractor.GetStructuredData()

	fmt.Printf("\n=== 提取摘要 ===\n")
	fmt.Printf("总字符数: %d\n", len(content))

	if dataTypes, ok := structuredData["dataTypes"].(map[string]string); ok {
		fmt.Printf("识别的数据类型: %d个\n", len(dataTypes))
	}

	if encodingRules, ok := structuredData["encodingRules"].(map[string]string); ok {
		fmt.Printf("识别的编码规则: %d个\n", len(encodingRules))
	}

	if structures, ok := structuredData["structures"].(map[string]interface{}); ok {
		fmt.Printf("识别的结构定义: %d个\n", len(structures))
	}
}

func printHelp() {
	fmt.Println("DLT 790.6-2010 A-XDR编码规则 PDF内容提取工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  go run cmd/pdf_extractor/main.go [选项]")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -pdf string")
	fmt.Println("        PDF文件路径 (默认: doc/DL T 790.6-2010 采用配电线载波的配电自动化 第6部分_ A-XDR编码规则 标准.pdf)")
	fmt.Println("  -output string")
	fmt.Println("        输出目录路径 (默认: internal/protocol/dlt69845/datatype)")
	fmt.Println("  -help")
	fmt.Println("        显示此帮助信息")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  # 使用默认参数")
	fmt.Println("  go run cmd/pdf_extractor/main.go")
	fmt.Println()
	fmt.Println("  # 指定自定义路径")
	fmt.Println("  go run cmd/pdf_extractor/main.go -pdf /path/to/document.pdf -output /path/to/output")
	fmt.Println()
	fmt.Println("输出文件:")
	fmt.Println("  - dlt790_6_raw_content.txt: PDF原始文本内容")
	fmt.Println("  - axdr_types.go: A-XDR基本数据类型Go定义")
	fmt.Println("  - axdr_encoding_rules.go: A-XDR编码规则说明")
	fmt.Println("  - axdr_structures.go: A-XDR结构定义")
}
