package main

import (
	"fmt"
	"log"

	"tp.service/internal/protocol/dlt69845/datatype"
)

// DeviceInfo 设备信息结构示例
type DeviceInfo struct {
	ID     uint16 // 设备ID
	Name   string // 设备名称
	Status bool   // 设备状态
	Values []int16 // 测量值数组
}

// MeterReading 电表读数结构示例
type MeterReading struct {
	MeterID   uint32  // 电表ID
	Timestamp datatype.Date // 时间戳
	Voltage   float32 // 电压值
	Current   float32 // 电流值
	Power     float32 // 功率值
	Energy    uint64  // 电能值
}

func main() {
	fmt.Println("A-XDR 数据类型使用示例")
	fmt.Println("基于 DL/T 790.6-2010 标准")
	fmt.Println(repeat("=", 60))

	// 创建编解码工具
	codec := datatype.NewAXDRCodecUtil()

	// 示例1: 设备信息编解码
	fmt.Println("\n=== 示例1: 设备信息编解码 ===")
	deviceExample(codec)

	// 示例2: 电表读数编解码
	fmt.Println("\n=== 示例2: 电表读数编解码 ===")
	meterReadingExample(codec)

	// 示例3: 复杂数据结构编解码
	fmt.Println("\n=== 示例3: 复杂数据结构编解码 ===")
	complexDataExample(codec)

	// 示例4: 协议帧构建
	fmt.Println("\n=== 示例4: DLT69845协议帧构建 ===")
	protocolFrameExample(codec)

	fmt.Println("\n所有示例完成！")
}

func deviceExample(codec *datatype.AXDRCodecUtil) {
	// 创建设备信息
	device := DeviceInfo{
		ID:     1001,
		Name:   "智能电表01",
		Status: true,
		Values: []int16{220, 5, 1100}, // 电压、电流、功率
	}

	fmt.Printf("原始设备信息: %+v\n", device)

	// 编码设备信息
	var encoded []byte

	// 编码设备ID
	idBytes := codec.EncodeLongUnsigned(device.ID)
	encoded = append(encoded, idBytes...)

	// 编码设备名称
	nameBytes := codec.EncodeUTF8String(device.Name)
	encoded = append(encoded, nameBytes...)

	// 编码设备状态
	statusBytes := codec.EncodeBoolean(device.Status)
	encoded = append(encoded, statusBytes...)

	// 编码数组长度
	arrayLenBytes := datatype.EncodeLength(uint64(len(device.Values)))
	encoded = append(encoded, arrayLenBytes...)

	// 编码数组元素
	for _, value := range device.Values {
		valueBytes := codec.EncodeLong(value)
		encoded = append(encoded, valueBytes...)
	}

	fmt.Printf("编码结果: %02X\n", encoded)
	fmt.Printf("编码长度: %d 字节\n", len(encoded))

	// 解码验证
	offset := 0

	// 解码设备ID
	decodedID, err := codec.DecodeLongUnsigned(encoded[offset:])
	if err != nil {
		log.Printf("解码设备ID失败: %v", err)
		return
	}
	offset += 2

	// 解码设备名称
	decodedName, consumed, err := codec.DecodeUTF8String(encoded[offset:])
	if err != nil {
		log.Printf("解码设备名称失败: %v", err)
		return
	}
	offset += consumed

	// 解码设备状态
	decodedStatus, err := codec.DecodeBoolean(encoded[offset:])
	if err != nil {
		log.Printf("解码设备状态失败: %v", err)
		return
	}
	offset += 1

	// 解码数组长度
	arrayLen, consumed, err := datatype.DecodeLength(encoded[offset:])
	if err != nil {
		log.Printf("解码数组长度失败: %v", err)
		return
	}
	offset += consumed

	// 解码数组元素
	var decodedValues []int16
	for i := uint64(0); i < arrayLen; i++ {
		value, err := codec.DecodeLong(encoded[offset:])
		if err != nil {
			log.Printf("解码数组元素失败: %v", err)
			return
		}
		decodedValues = append(decodedValues, value)
		offset += 2
	}

	fmt.Printf("解码结果: ID=%d, Name=%s, Status=%v, Values=%v\n",
		decodedID, decodedName, decodedStatus, decodedValues)
}

func meterReadingExample(codec *datatype.AXDRCodecUtil) {
	// 创建电表读数
	reading := MeterReading{
		MeterID: 12345678,
		Timestamp: datatype.Date{
			Year:        2024,
			Month:       6,
			Day:         28,
			DayOfWeek:   5,
			Hour:        15,
			Minute:      30,
			Second:      45,
			Hundredths:  0,
			Deviation:   480, // UTC+8
			ClockStatus: 0,
		},
		Voltage: 220.5,
		Current: 4.8,
		Power:   1058.4,
		Energy:  123456789,
	}

	fmt.Printf("原始电表读数: %+v\n", reading)

	// 编码电表读数
	var encoded []byte

	// 编码电表ID
	encoded = append(encoded, codec.EncodeDoubleLongUnsigned(reading.MeterID)...)

	// 编码时间戳
	encoded = append(encoded, codec.EncodeDate(reading.Timestamp)...)

	// 编码电压
	encoded = append(encoded, codec.EncodeFloat32(reading.Voltage)...)

	// 编码电流
	encoded = append(encoded, codec.EncodeFloat32(reading.Current)...)

	// 编码功率
	encoded = append(encoded, codec.EncodeFloat32(reading.Power)...)

	// 编码电能
	encoded = append(encoded, codec.EncodeLong64Unsigned(reading.Energy)...)

	fmt.Printf("编码结果: %02X\n", encoded)
	fmt.Printf("编码长度: %d 字节\n", len(encoded))

	// 解码验证
	offset := 0

	decodedMeterID, err := codec.DecodeDoubleLongUnsigned(encoded[offset:])
	if err != nil {
		log.Printf("解码电表ID失败: %v", err)
		return
	}
	offset += 4

	decodedTimestamp, err := codec.DecodeDate(encoded[offset:])
	if err != nil {
		log.Printf("解码时间戳失败: %v", err)
		return
	}
	offset += 12

	decodedVoltage, err := codec.DecodeFloat32(encoded[offset:])
	if err != nil {
		log.Printf("解码电压失败: %v", err)
		return
	}
	offset += 4

	decodedCurrent, err := codec.DecodeFloat32(encoded[offset:])
	if err != nil {
		log.Printf("解码电流失败: %v", err)
		return
	}
	offset += 4

	decodedPower, err := codec.DecodeFloat32(encoded[offset:])
	if err != nil {
		log.Printf("解码功率失败: %v", err)
		return
	}
	offset += 4

	decodedEnergy, err := codec.DecodeLong64Unsigned(encoded[offset:])
	if err != nil {
		log.Printf("解码电能失败: %v", err)
		return
	}

	fmt.Printf("解码结果:\n")
	fmt.Printf("  电表ID: %d\n", decodedMeterID)
	fmt.Printf("  时间戳: %04d-%02d-%02d %02d:%02d:%02d.%02d\n",
		decodedTimestamp.Year, decodedTimestamp.Month, decodedTimestamp.Day,
		decodedTimestamp.Hour, decodedTimestamp.Minute, decodedTimestamp.Second,
		decodedTimestamp.Hundredths)
	fmt.Printf("  电压: %.2f V\n", decodedVoltage)
	fmt.Printf("  电流: %.2f A\n", decodedCurrent)
	fmt.Printf("  功率: %.2f W\n", decodedPower)
	fmt.Printf("  电能: %d Wh\n", decodedEnergy)
}

func complexDataExample(codec *datatype.AXDRCodecUtil) {
	// 复杂数据结构示例：多个设备的批量数据
	fmt.Println("编码多设备批量数据...")

	devices := []DeviceInfo{
		{ID: 1001, Name: "设备A", Status: true, Values: []int16{220, 5}},
		{ID: 1002, Name: "设备B", Status: false, Values: []int16{218, 0}},
		{ID: 1003, Name: "设备C", Status: true, Values: []int16{222, 8}},
	}

	var encoded []byte

	// 编码设备数量
	deviceCountBytes := datatype.EncodeLength(uint64(len(devices)))
	encoded = append(encoded, deviceCountBytes...)

	// 编码每个设备
	for _, device := range devices {
		// 编码设备ID
		encoded = append(encoded, codec.EncodeLongUnsigned(device.ID)...)
		// 编码设备名称
		encoded = append(encoded, codec.EncodeUTF8String(device.Name)...)
		// 编码设备状态
		encoded = append(encoded, codec.EncodeBoolean(device.Status)...)
		// 编码值数组长度
		encoded = append(encoded, datatype.EncodeLength(uint64(len(device.Values)))...)
		// 编码值数组
		for _, value := range device.Values {
			encoded = append(encoded, codec.EncodeLong(value)...)
		}
	}

	fmt.Printf("批量数据编码结果: %02X\n", encoded)
	fmt.Printf("总编码长度: %d 字节\n", len(encoded))
	fmt.Printf("平均每设备: %.1f 字节\n", float64(len(encoded))/float64(len(devices)))
}

func protocolFrameExample(codec *datatype.AXDRCodecUtil) {
	// 构建DLT69845协议帧示例
	fmt.Println("构建DLT69845协议帧...")

	// 帧起始字符
	fsc := datatype.FSC(0x68)

	// 帧长度域 (假设数据长度为50字节)
	fld := datatype.FLD{
		Unit:  0,           // 单位为字节
		Value: [2]byte{0, 50}, // 长度50字节
	}

	// 帧控制域
	fcd := datatype.FCD{
		Direction: 0, // 客户机发往服务器
		StartFlag: 1, // 客户机发起
		FDFlag:    0, // 完整的APDU
		SCFlag:    0, // 不加扰码
		FuncCode:  3, // 用户数据
	}

	// 帧地址域
	fad := datatype.FAD{
		SA: struct {
			Type   byte
			LAFlag byte
			Len    byte
			Addr   []byte
		}{
			Type:   0,                    // 单地址
			LAFlag: 0,                    // 无逻辑扩展地址
			Len:    3,                    // 4字节地址长度 (3表示4字节)
			Addr:   []byte{0x01, 0x02, 0x03, 0x04}, // 地址
		},
		CA: 0x01, // 客户端地址
	}

	// 帧头校验 (简化计算)
	hcs := datatype.HCS{0x12, 0x34}

	// 构建帧头
	var frame []byte
	frame = append(frame, byte(fsc))
	frame = append(frame, fld.Unit)
	frame = append(frame, fld.Value[:]...)
	frame = append(frame, fcd.Direction<<7|fcd.StartFlag<<6|fcd.FDFlag<<5|fcd.SCFlag<<4|fcd.FuncCode)
	frame = append(frame, fad.SA.Type<<6|fad.SA.LAFlag<<4|fad.SA.Len)
	frame = append(frame, fad.SA.Addr...)
	frame = append(frame, fad.CA)
	frame = append(frame, hcs[:]...)

	// 添加示例用户数据 (A-XDR编码的设备信息)
	deviceData := codec.EncodeLongUnsigned(1001)                    // 设备ID
	deviceData = append(deviceData, codec.EncodeUTF8String("测试设备")...) // 设备名称
	deviceData = append(deviceData, codec.EncodeBoolean(true)...)    // 设备状态
	frame = append(frame, deviceData...)

	// 帧校验符 (简化计算)
	fcs := datatype.FCS{0x56, 0x78}
	frame = append(frame, fcs[:]...)

	// 帧结束符
	fec := datatype.FEC(0x16)
	frame = append(frame, byte(fec))

	fmt.Printf("协议帧结构:\n")
	fmt.Printf("  帧起始字符 (FSC): %02X\n", fsc)
	fmt.Printf("  帧长度域 (FLD): %02X %02X %02X\n", fld.Unit, fld.Value[0], fld.Value[1])
	fmt.Printf("  帧控制域 (FCD): %02X\n", fcd.Direction<<7|fcd.StartFlag<<6|fcd.FDFlag<<5|fcd.SCFlag<<4|fcd.FuncCode)
	fmt.Printf("  帧地址域 (FAD): %02X %02X %02X %02X %02X %02X\n",
		fad.SA.Type<<6|fad.SA.LAFlag<<4|fad.SA.Len,
		fad.SA.Addr[0], fad.SA.Addr[1], fad.SA.Addr[2], fad.SA.Addr[3], fad.CA)
	fmt.Printf("  帧头校验 (HCS): %02X %02X\n", hcs[0], hcs[1])
	fmt.Printf("  用户数据: %02X\n", deviceData)
	fmt.Printf("  帧校验符 (FCS): %02X %02X\n", fcs[0], fcs[1])
	fmt.Printf("  帧结束符 (FEC): %02X\n", fec)
	fmt.Printf("\n完整协议帧: %02X\n", frame)
	fmt.Printf("帧总长度: %d 字节\n", len(frame))
}

// 辅助函数：重复字符串
func repeat(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}
