package main

import (
	"fmt"

	"tp.service/internal/protocol/dlt69845/datatype"
)

func main() {
	fmt.Println("A-XDR编解码测试程序")
	fmt.Println("基于 DL/T 790.6-2010 标准")
	fmt.Println(repeat("=", 50))

	// 创建编解码工具
	codec := datatype.NewAXDRCodecUtil()

	// 测试基本数据类型
	testBasicTypes(codec)

	// 测试字符串类型
	testStringTypes(codec)

	// 测试时间日期类型
	testDateTimeTypes(codec)

	// 测试BCD编码
	testBCDEncoding(codec)

	// 测试长度编码
	testLengthEncoding()

	fmt.Println("\n所有测试完成！")
}

func testBasicTypes(codec *datatype.AXDRCodecUtil) {
	fmt.Println("\n=== 基本数据类型测试 ===")

	// 测试布尔值
	fmt.Println("\n1. Boolean 测试:")
	testBoolean := true
	encodedBool := codec.EncodeBoolean(testBoolean)
	decodedBool, err := codec.DecodeBoolean(encodedBool)
	fmt.Printf("原值: %v, 编码: %02X, 解码: %v, 错误: %v\n",
		testBoolean, encodedBool, decodedBool, err)

	// 测试8位整数
	fmt.Println("\n2. Integer 测试:")
	testInt := int8(-50)
	encodedInt := codec.EncodeInteger(testInt)
	decodedInt, err := codec.DecodeInteger(encodedInt)
	fmt.Printf("原值: %d, 编码: %02X, 解码: %d, 错误: %v\n",
		testInt, encodedInt, decodedInt, err)

	// 测试8位无符号整数
	fmt.Println("\n3. Unsigned 测试:")
	testUint := uint8(200)
	encodedUint := codec.EncodeUnsigned(testUint)
	decodedUint, err := codec.DecodeUnsigned(encodedUint)
	fmt.Printf("原值: %d, 编码: %02X, 解码: %d, 错误: %v\n",
		testUint, encodedUint, decodedUint, err)

	// 测试16位整数
	fmt.Println("\n4. Long 测试:")
	testLong := int16(-1000)
	encodedLong := codec.EncodeLong(testLong)
	decodedLong, err := codec.DecodeLong(encodedLong)
	fmt.Printf("原值: %d, 编码: %02X, 解码: %d, 错误: %v\n",
		testLong, encodedLong, decodedLong, err)

	// 测试16位无符号整数
	fmt.Println("\n5. Long-Unsigned 测试:")
	testLongUint := uint16(50000)
	encodedLongUint := codec.EncodeLongUnsigned(testLongUint)
	decodedLongUint, err := codec.DecodeLongUnsigned(encodedLongUint)
	fmt.Printf("原值: %d, 编码: %02X, 解码: %d, 错误: %v\n",
		testLongUint, encodedLongUint, decodedLongUint, err)

	// 测试32位整数
	fmt.Println("\n6. Double-Long 测试:")
	testDLong := int32(-100000)
	encodedDLong := codec.EncodeDoubleLong(testDLong)
	decodedDLong, err := codec.DecodeDoubleLong(encodedDLong)
	fmt.Printf("原值: %d, 编码: %02X, 解码: %d, 错误: %v\n",
		testDLong, encodedDLong, decodedDLong, err)

	// 测试32位浮点数
	fmt.Println("\n7. Float32 测试:")
	testFloat32 := float32(3.14159)
	encodedFloat32 := codec.EncodeFloat32(testFloat32)
	decodedFloat32, err := codec.DecodeFloat32(encodedFloat32)
	fmt.Printf("原值: %f, 编码: %02X, 解码: %f, 错误: %v\n",
		testFloat32, encodedFloat32, decodedFloat32, err)

	// 测试64位浮点数
	fmt.Println("\n8. Float64 测试:")
	testFloat64 := 3.141592653589793
	encodedFloat64 := codec.EncodeFloat64(testFloat64)
	decodedFloat64, err := codec.DecodeFloat64(encodedFloat64)
	fmt.Printf("原值: %f, 编码: %02X, 解码: %f, 错误: %v\n",
		testFloat64, encodedFloat64, decodedFloat64, err)
}

func testStringTypes(codec *datatype.AXDRCodecUtil) {
	fmt.Println("\n=== 字符串类型测试 ===")

	// 测试八位字节串
	fmt.Println("\n1. Octet-String 测试:")
	testOctet := []byte{0x01, 0x02, 0x03, 0x04, 0x05}
	encodedOctet := codec.EncodeOctetString(testOctet)
	decodedOctet, consumed, err := codec.DecodeOctetString(encodedOctet)
	fmt.Printf("原值: %02X, 编码: %02X, 解码: %02X, 消耗字节: %d, 错误: %v\n",
		testOctet, encodedOctet, decodedOctet, consumed, err)

	// 测试可见字符串
	fmt.Println("\n2. Visible-String 测试:")
	testVisible := "Hello World"
	encodedVisible := codec.EncodeVisibleString(testVisible)
	decodedVisible, consumed, err := codec.DecodeVisibleString(encodedVisible)
	fmt.Printf("原值: %s, 编码: %02X, 解码: %s, 消耗字节: %d, 错误: %v\n",
		testVisible, encodedVisible, decodedVisible, consumed, err)

	// 测试UTF-8字符串
	fmt.Println("\n3. UTF8-String 测试:")
	testUTF8 := "你好世界"
	encodedUTF8 := codec.EncodeUTF8String(testUTF8)
	decodedUTF8, consumed, err := codec.DecodeUTF8String(encodedUTF8)
	fmt.Printf("原值: %s, 编码: %02X, 解码: %s, 消耗字节: %d, 错误: %v\n",
		testUTF8, encodedUTF8, decodedUTF8, consumed, err)
}

func testDateTimeTypes(codec *datatype.AXDRCodecUtil) {
	fmt.Println("\n=== 时间日期类型测试 ===")

	// 测试日期
	fmt.Println("\n1. Date 测试:")
	testDate := datatype.Date{
		Year:        2024,
		Month:       6,
		Day:         28,
		DayOfWeek:   5, // 星期五
		Hour:        14,
		Minute:      30,
		Second:      25,
		Hundredths:  50,
		Deviation:   480, // UTC+8
		ClockStatus: 0,
	}
	encodedDate := codec.EncodeDate(testDate)
	decodedDate, err := codec.DecodeDate(encodedDate)
	fmt.Printf("原值: %+v\n", testDate)
	fmt.Printf("编码: %02X\n", encodedDate)
	fmt.Printf("解码: %+v, 错误: %v\n", decodedDate, err)

	// 测试时间
	fmt.Println("\n2. Time 测试:")
	testTime := datatype.Time{
		Hour:       14,
		Minute:     30,
		Second:     25,
		Hundredths: 50,
	}
	encodedTime := codec.EncodeTime(testTime)
	decodedTime, err := codec.DecodeTime(encodedTime)
	fmt.Printf("原值: %+v\n", testTime)
	fmt.Printf("编码: %02X\n", encodedTime)
	fmt.Printf("解码: %+v, 错误: %v\n", decodedTime, err)
}

func testBCDEncoding(codec *datatype.AXDRCodecUtil) {
	fmt.Println("\n=== BCD编码测试 ===")

	testNumbers := []string{"1234", "567", "98765432", "0"}

	for i, num := range testNumbers {
		fmt.Printf("\n%d. BCD 测试 - %s:\n", i+1, num)
		encoded, err := codec.EncodeBCD(num)
		if err != nil {
			fmt.Printf("编码错误: %v\n", err)
			continue
		}
		decoded := codec.DecodeBCD(encoded)
		fmt.Printf("原值: %s, 编码: %02X, 解码: %s\n", num, encoded, decoded)
	}
}

func testLengthEncoding() {
	fmt.Println("\n=== 长度编码测试 ===")

	testLengths := []uint64{0, 50, 127, 128, 255, 256, 1000, 65535, 100000}

	for i, length := range testLengths {
		fmt.Printf("\n%d. 长度编码测试 - %d:\n", i+1, length)
		encoded := datatype.EncodeLength(length)
		decoded, consumed, err := datatype.DecodeLength(encoded)
		fmt.Printf("原值: %d, 编码: %02X, 解码: %d, 消耗字节: %d, 错误: %v\n",
			length, encoded, decoded, consumed, err)
	}
}

// 辅助函数：重复字符串
func repeat(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}

func init() {
	// 重新定义字符串重复函数，因为Go没有内置的字符串重复操作符
	// 这里我们用一个简单的实现
}
