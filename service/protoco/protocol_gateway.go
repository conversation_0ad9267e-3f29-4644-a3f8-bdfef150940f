/**
 * 协议服务网关管理器
 * 提供协议服务的注册、发现和管理功能
 */
package service

import (
	"context"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	proto "tp.service/service/bean/api/protocol"
)

// ProtocolGateway 协议服务网关
type ProtocolGateway struct {
	registry *ProtocolServiceRegistry
	config   *GatewayConfig
}

// GatewayConfig 网关配置
type GatewayConfig struct {
	// 服务配置列表
	Services map[string]ServiceConfig `json:"services"`
	// 健康检查间隔
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	// 连接超时
	ConnectionTimeout time.Duration `json:"connection_timeout"`
}

// ServiceConfig 服务配置
type ServiceConfig struct {
	// 服务地址 (如果为空则使用内置服务)
	Address string `json:"address,omitempty"`
	// 是否启用
	Enabled bool `json:"enabled"`
	// 重试次数
	RetryCount int `json:"retry_count"`
}

// NewProtocolGateway 创建协议服务网关
func NewProtocolGateway(config *GatewayConfig) *ProtocolGateway {
	if config == nil {
		config = getDefaultConfig()
	}

	gateway := &ProtocolGateway{
		registry: &ProtocolServiceRegistry{
			services: make(map[string]ProtocolServiceClient),
		},
		config: config,
	}

	// 初始化服务
	gateway.initializeServices()

	// 启动健康检查
	go gateway.startHealthCheck()

	return gateway
}

// getDefaultConfig 获取默认配置
func getDefaultConfig() *GatewayConfig {
	return &GatewayConfig{
		Services: map[string]ServiceConfig{
			"DL/T 698.45": {
				Address:    "", // 空地址表示使用内置服务
				Enabled:    true,
				RetryCount: 3,
			},
			"Modbus": {
				Address:    "modbus-service:50052", // 有地址表示使用远程服务
				Enabled:    false,
				RetryCount: 3,
			},
			"DL/T 645": {
				Address:    "dlt645-service:50053",
				Enabled:    false,
				RetryCount: 3,
			},
		},
		HealthCheckInterval: 30 * time.Second,
		ConnectionTimeout:   5 * time.Second,
	}
}

// initializeServices 初始化服务
func (g *ProtocolGateway) initializeServices() {
	for serviceName, config := range g.config.Services {
		if !config.Enabled {
			continue
		}

		switch serviceName {
		case "DL/T 698.45":
			g.registerService(serviceName, config, g.createDLT69845Service)
		case "Modbus":
			g.registerService(serviceName, config, g.createModbusService)
		case "DL/T 645":
			g.registerService(serviceName, config, g.createDLT645Service)
		default:
			log.Printf("Unknown service: %s", serviceName)
		}
	}
}

// ServiceFactory 服务工厂函数类型
type ServiceFactory func() ProtocolServiceClient

// registerService 统一的服务注册方法
func (g *ProtocolGateway) registerService(serviceName string, config ServiceConfig, factory ServiceFactory) {
	var client ProtocolServiceClient

	if config.Address != "" {
		// 尝试连接远程服务
		client = g.createRemoteClient(serviceName, config)
		if client == nil {
			// 远程服务不可用，使用内置服务
			client = factory()
			log.Printf("Remote %s service not available, using built-in service", serviceName)
		} else {
			log.Printf("Registered remote %s service at %s", serviceName, config.Address)
		}
	} else {
		// 直接使用内置服务
		client = factory()
		log.Printf("Registered built-in %s service", serviceName)
	}

	if client != nil {
		g.registry.RegisterService(serviceName, client)
	}
}

// createRemoteClient 创建远程客户端
func (g *ProtocolGateway) createRemoteClient(serviceName string, config ServiceConfig) ProtocolServiceClient {
	ctx, cancel := context.WithTimeout(context.Background(), g.config.ConnectionTimeout)
	defer cancel()

	conn, err := grpc.NewClient(config.Address,
		grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Printf("Failed to connect to remote %s service: %v", serviceName, err)
		return nil
	}

	// 根据服务名称创建对应的客户端
	switch serviceName {
	case "DL/T 698.45":
		client := proto.NewDLT69845ProtSvcClient(conn)
		// 测试连接
		_, err = client.GetSvcInfo(ctx, &proto.GetSvcInfoReq{})
		if err != nil {
			log.Printf("Remote %s service not responding: %v", serviceName, err)
			conn.Close()
			return nil
		}
		return &DLT69845ServiceClient{client: client}
	default:
		log.Printf("Unknown remote service type: %s", serviceName)
		conn.Close()
		return nil
	}
}

// createDLT69845Service 创建 DL/T 698.45 服务
func (g *ProtocolGateway) createDLT69845Service() ProtocolServiceClient {
	localService := NewDLT69845ProtSvcServer()
	return &LocalDLT69845ServiceClient{service: localService}
}

// createModbusService 创建 Modbus 服务 (示例)
func (g *ProtocolGateway) createModbusService() ProtocolServiceClient {
	// TODO: 实现 Modbus 服务创建
	log.Printf("Modbus service not implemented yet")
	return nil
}

// createDLT645Service 创建 DL/T 645 服务 (示例)
func (g *ProtocolGateway) createDLT645Service() ProtocolServiceClient {
	// TODO: 实现 DL/T 645 服务创建
	log.Printf("DL/T 645 service not implemented yet")
	return nil
}

// startHealthCheck 启动健康检查
func (g *ProtocolGateway) startHealthCheck() {
	ticker := time.NewTicker(g.config.HealthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		g.performHealthCheck()
	}
}

// performHealthCheck 执行健康检查
func (g *ProtocolGateway) performHealthCheck() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	services := g.registry.GetAllServices()
	for serviceName, client := range services {
		_, err := client.GetServiceInfo(ctx)
		if err != nil {
			log.Printf("Health check failed for service %s: %v", serviceName, err)
			// TODO: 实现服务重连逻辑
		}
	}
}

// GetRegistry 获取服务注册表
func (g *ProtocolGateway) GetRegistry() *ProtocolServiceRegistry {
	return g.registry
}

// AddService 动态添加服务
func (g *ProtocolGateway) AddService(name string, client ProtocolServiceClient) {
	g.registry.RegisterService(name, client)
	log.Printf("Dynamically added service: %s", name)
}

// RemoveService 动态移除服务
func (g *ProtocolGateway) RemoveService(name string) {
	g.registry.mutex.Lock()
	defer g.registry.mutex.Unlock()

	if _, exists := g.registry.services[name]; exists {
		delete(g.registry.services, name)
		log.Printf("Removed service: %s", name)
	}
}

// GetServiceStatus 获取服务状态
func (g *ProtocolGateway) GetServiceStatus() map[string]bool {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	status := make(map[string]bool)
	services := g.registry.GetAllServices()

	for serviceName, client := range services {
		_, err := client.GetServiceInfo(ctx)
		status[serviceName] = err == nil
	}

	return status
}

// ListServices 列出所有服务
func (g *ProtocolGateway) ListServices() []string {
	g.registry.mutex.RLock()
	defer g.registry.mutex.RUnlock()

	var services []string
	for name := range g.registry.services {
		services = append(services, name)
	}

	return services
}
