/**
 * 协议服务网关使用示例
 */
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"gorm.io/gorm"
	proto "tp.service/service/bean/api/protocol"
)

// ExampleUsage 协议服务网关使用示例
func ExampleUsage() {
	// 1. 创建数据库连接 (示例)
	var db *gorm.DB // 实际使用时需要初始化数据库连接

	// 2. 使用默认配置创建协议服务器
	server := NewProtocolServer(db)

	// 3. 或者使用自定义配置
	customConfig := &GatewayConfig{
		Services: map[string]ServiceConfig{
			"DL/T 698.45": {
				Address:    "", // 空地址使用内置服务
				Enabled:    true,
				RetryCount: 3,
			},
			"Modbus": {
				Address:    "modbus-service:50052", // 有地址使用远程服务
				Enabled:    false,                  // 暂时禁用
				RetryCount: 3,
			},
		},
		HealthCheckInterval: 60 * time.Second,
		ConnectionTimeout:   10 * time.Second,
	}
	serverWithConfig := NewProtocolServerWithConfig(db, customConfig)

	// 4. 获取服务信息
	ctx := context.Background()

	// 获取所有服务信息
	allServicesResp, err := server.GetServiceInfo(ctx, &proto.GetServiceInfoRequest{Name: ""})
	if err != nil {
		log.Printf("Failed to get all services: %v", err)
	} else {
		fmt.Printf("Available services: %d\n", len(allServicesResp.Services))
		for _, service := range allServicesResp.Services {
			fmt.Printf("- %s (v%s): %s\n", service.Name, service.Version, service.Description)
		}
	}

	// 获取特定服务信息
	dltServiceResp, err := server.GetServiceInfo(ctx, &proto.GetServiceInfoRequest{Name: "DL/T 698.45"})
	if err != nil {
		log.Printf("Failed to get DL/T 698.45 service: %v", err)
	} else {
		fmt.Printf("DL/T 698.45 service found: %s\n", dltServiceResp.Services[0].Description)
	}

	// 5. 使用协议服务进行数据组织
	params := map[string]interface{}{
		"type":    0x0100, // 登录请求
		"addr":    "123456789012",
		"data":    map[string]interface{}{"username": "admin", "password": "123456"},
		"timetag": 0,
	}
	paramsJSON, _ := json.Marshal(params)

	// 协议数据域组织
	ddoResp, err := server.ProtocolDataDomainOrganization(ctx, &proto.CPDORequest{
		Name:   "DL/T 698.45",
		Params: string(paramsJSON),
	})
	if err != nil {
		log.Printf("Failed to organize data domain: %v", err)
	} else {
		fmt.Printf("Organized data domain: %s\n", ddoResp.Datadomain)
	}

	// 协议组织
	poResp, err := server.ProtocolOrganization(ctx, &proto.CPORequest{
		Name:   "DL/T 698.45",
		Params: string(paramsJSON),
	})
	if err != nil {
		log.Printf("Failed to organize protocol: %v", err)
	} else {
		fmt.Printf("Organized protocol frame: %s\n", poResp.Frame)
	}

	// 6. 使用协议服务进行数据解析
	testFrame := "68123456789012681000010203040516"
	ppResp, err := server.ProtocolParsing(ctx, &proto.CPPRequest{
		Name:  "DL/T 698.45",
		Frame: testFrame,
	})
	if err != nil {
		log.Printf("Failed to parse protocol: %v", err)
	} else {
		fmt.Printf("Parsed protocol data: %s\n", ppResp.Data)
	}

	// 7. 网关管理功能
	gateway := server.GetGateway()

	// 获取服务状态
	status := gateway.GetServiceStatus()
	fmt.Println("Service status:")
	for serviceName, isHealthy := range status {
		fmt.Printf("- %s: %v\n", serviceName, isHealthy)
	}

	// 列出所有服务
	services := gateway.ListServices()
	fmt.Printf("Registered services: %v\n", services)

	// 8. 动态添加服务 (示例)
	// customClient := &CustomProtocolClient{...}
	// gateway.AddService("Custom Protocol", customClient)

	_ = serverWithConfig // 避免未使用变量警告
}

// CustomProtocolClient 自定义协议客户端示例
type CustomProtocolClient struct {
	name        string
	version     string
	description string
}

func (c *CustomProtocolClient) GetServiceInfo(ctx context.Context) (*proto.ProtServiceInfo, error) {
	return &proto.ProtServiceInfo{
		Name:        c.name,
		Version:     c.version,
		Description: c.description,
	}, nil
}

func (c *CustomProtocolClient) ProtocolDataDomainOrganization(ctx context.Context, params string) (string, error) {
	return fmt.Sprintf("CUSTOM_DATADOMAIN_%s", params), nil
}

func (c *CustomProtocolClient) ProtocolOrganization(ctx context.Context, params string) (string, error) {
	return fmt.Sprintf("CUSTOM_FRAME_%s", params), nil
}

func (c *CustomProtocolClient) ProtocolParsing(ctx context.Context, frame string) (string, error) {
	return fmt.Sprintf(`{"frame": "%s", "protocol": "custom", "parsed": true}`, frame), nil
}

func (c *CustomProtocolClient) ProtocolDataDomainParsing(ctx context.Context, datadomain string) (string, error) {
	return fmt.Sprintf(`{"datadomain": "%s", "protocol": "custom", "parsed": true}`, datadomain), nil
}

// CreateCustomProtocolClient 创建自定义协议客户端
func CreateCustomProtocolClient(name, version, description string) *CustomProtocolClient {
	return &CustomProtocolClient{
		name:        name,
		version:     version,
		description: description,
	}
}

// DemoProtocolGatewayFeatures 演示协议网关功能
func DemoProtocolGatewayFeatures() {
	fmt.Println("=== 协议服务网关功能演示 ===")

	// 创建网关
	gateway := NewProtocolGateway(nil)

	// 添加自定义协议
	customClient := CreateCustomProtocolClient("Demo Protocol", "1.0.0", "演示协议服务")
	gateway.AddService("Demo Protocol", customClient)

	// 测试服务
	ctx := context.Background()

	// 获取服务信息
	if client, exists := gateway.GetRegistry().GetService("Demo Protocol"); exists {
		info, err := client.GetServiceInfo(ctx)
		if err == nil {
			fmt.Printf("自定义协议服务: %s v%s - %s\n", info.Name, info.Version, info.Description)
		}

		// 测试协议组织
		frame, err := client.ProtocolOrganization(ctx, `{"test": "data"}`)
		if err == nil {
			fmt.Printf("协议组织结果: %s\n", frame)
		}

		// 测试协议解析
		data, err := client.ProtocolParsing(ctx, "test_frame_data")
		if err == nil {
			fmt.Printf("协议解析结果: %s\n", data)
		}
	}

	// 显示所有服务
	fmt.Printf("当前注册的服务: %v\n", gateway.ListServices())

	// 显示服务状态
	status := gateway.GetServiceStatus()
	fmt.Println("服务健康状态:")
	for name, healthy := range status {
		fmt.Printf("- %s: %v\n", name, healthy)
	}
}
