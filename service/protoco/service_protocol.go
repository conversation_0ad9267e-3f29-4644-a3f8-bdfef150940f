/**
 * 协议服务网关
 */
package service

import (
	"context"
	"fmt"
	"log"
	"sync"

	"gorm.io/gorm"
	proto "tp.service/service/bean/api/protocol"
)

// ProtocolServiceRegistry 协议服务注册表
type ProtocolServiceRegistry struct {
	services map[string]ProtocolServiceClient
	mutex    sync.RWMutex
}

// ProtocolServiceClient 协议服务客户端接口
type ProtocolServiceClient interface {
	GetServiceInfo(ctx context.Context) (*proto.ProtServiceInfo, error)
	ProtocolDataDomainOrganization(ctx context.Context, params string) (string, error)
	ProtocolOrganization(ctx context.Context, params string) (string, error)
	ProtocolParsing(ctx context.Context, frame string) (string, error)
	ProtocolDataDomainParsing(ctx context.Context, datadomain string) (string, error)
}

// DLT69845ServiceClient DL/T 698.45 协议服务客户端适配器
type DLT69845ServiceClient struct {
	client proto.DLT69845ProtSvcClient
}

func (c *DLT69845ServiceClient) GetServiceInfo(ctx context.Context) (*proto.ProtServiceInfo, error) {
	resp, err := c.client.GetSvcInfo(ctx, &proto.GetSvcInfoReq{})
	if err != nil {
		return nil, err
	}
	return resp.Services, nil
}

func (c *DLT69845ServiceClient) ProtocolDataDomainOrganization(ctx context.Context, params string) (string, error) {
	resp, err := c.client.ProtDDO(ctx, &proto.CPDOReq{Params: params})
	if err != nil {
		return "", err
	}
	return resp.Datadomain, nil
}

func (c *DLT69845ServiceClient) ProtocolOrganization(ctx context.Context, params string) (string, error) {
	resp, err := c.client.ProtO(ctx, &proto.CPOReq{Params: params})
	if err != nil {
		return "", err
	}
	return resp.Frame, nil
}

func (c *DLT69845ServiceClient) ProtocolParsing(ctx context.Context, frame string) (string, error) {
	resp, err := c.client.ProtP(ctx, &proto.CPPReq{Frame: frame})
	if err != nil {
		return "", err
	}
	return resp.Data, nil
}

func (c *DLT69845ServiceClient) ProtocolDataDomainParsing(ctx context.Context, datadomain string) (string, error) {
	resp, err := c.client.ProtDDP(ctx, &proto.CPDReq{Datadomain: datadomain})
	if err != nil {
		return "", err
	}
	return resp.Data, nil
}

// LocalDLT69845ServiceClient 本地 DL/T 698.45 协议服务客户端适配器
type LocalDLT69845ServiceClient struct {
	service *DLT69845ProtSvc
}

func (c *LocalDLT69845ServiceClient) GetServiceInfo(ctx context.Context) (*proto.ProtServiceInfo, error) {
	resp, err := c.service.GetSvcInfo(ctx, &proto.GetSvcInfoReq{})
	if err != nil {
		return nil, err
	}
	return resp.Services, nil
}

func (c *LocalDLT69845ServiceClient) ProtocolDataDomainOrganization(ctx context.Context, params string) (string, error) {
	resp, err := c.service.ProtDDO(ctx, &proto.CPDOReq{Params: params})
	if err != nil {
		return "", err
	}
	return resp.Datadomain, nil
}

func (c *LocalDLT69845ServiceClient) ProtocolOrganization(ctx context.Context, params string) (string, error) {
	resp, err := c.service.ProtO(ctx, &proto.CPOReq{Params: params})
	if err != nil {
		return "", err
	}
	return resp.Frame, nil
}

func (c *LocalDLT69845ServiceClient) ProtocolParsing(ctx context.Context, frame string) (string, error) {
	resp, err := c.service.ProtP(ctx, &proto.CPPReq{Frame: frame})
	if err != nil {
		return "", err
	}
	return resp.Data, nil
}

func (c *LocalDLT69845ServiceClient) ProtocolDataDomainParsing(ctx context.Context, datadomain string) (string, error) {
	resp, err := c.service.ProtDDP(ctx, &proto.CPDReq{Datadomain: datadomain})
	if err != nil {
		return "", err
	}
	return resp.Data, nil
}

type ProtocolServer struct {
	proto.UnimplementedBaseProtocolServiceServer
	gateway *ProtocolGateway
	db      *gorm.DB
}

func NewProtocolServer(db *gorm.DB) *ProtocolServer {
	// 创建协议网关
	gateway := NewProtocolGateway(nil) // 使用默认配置

	return &ProtocolServer{
		gateway: gateway,
		db:      db,
	}
}

// NewProtocolServerWithConfig 使用自定义配置创建协议服务器
func NewProtocolServerWithConfig(db *gorm.DB, config *GatewayConfig) *ProtocolServer {
	gateway := NewProtocolGateway(config)

	return &ProtocolServer{
		gateway: gateway,
		db:      db,
	}
}

// RegisterService 注册协议服务
func (r *ProtocolServiceRegistry) RegisterService(name string, client ProtocolServiceClient) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.services[name] = client
}

// GetService 获取协议服务
func (r *ProtocolServiceRegistry) GetService(name string) (ProtocolServiceClient, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	client, exists := r.services[name]
	return client, exists
}

// GetAllServices 获取所有协议服务
func (r *ProtocolServiceRegistry) GetAllServices() map[string]ProtocolServiceClient {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	services := make(map[string]ProtocolServiceClient)
	for name, client := range r.services {
		services[name] = client
	}
	return services
}

// 获取服务信息
func (s *ProtocolServer) GetServiceInfo(ctx context.Context, req *proto.GetServiceInfoRequest) (*proto.GetServiceInfoResponse, error) {
	if req.Name == "" {
		// 获取所有服务信息
		var services []*proto.ProtServiceInfo
		allServices := s.gateway.GetRegistry().GetAllServices()

		for _, client := range allServices {
			serviceInfo, err := client.GetServiceInfo(ctx)
			if err != nil {
				log.Printf("Failed to get service info: %v", err)
				continue
			}
			services = append(services, serviceInfo)
		}

		return &proto.GetServiceInfoResponse{
			Services: services,
		}, nil
	}

	// 获取指定服务信息
	client, exists := s.gateway.GetRegistry().GetService(req.Name)
	if !exists {
		return nil, fmt.Errorf("service not found: %s", req.Name)
	}

	serviceInfo, err := client.GetServiceInfo(ctx)
	if err != nil {
		return nil, err
	}

	return &proto.GetServiceInfoResponse{
		Services: []*proto.ProtServiceInfo{serviceInfo},
	}, nil
}

// 协议数据域组织
func (s *ProtocolServer) ProtocolDataDomainOrganization(ctx context.Context, req *proto.CPDORequest) (*proto.CPDOResponse, error) {
	client, exists := s.gateway.GetRegistry().GetService(req.Name)
	if !exists {
		return nil, fmt.Errorf("service not found: %s", req.Name)
	}

	datadomain, err := client.ProtocolDataDomainOrganization(ctx, req.Params)
	if err != nil {
		return nil, err
	}

	return &proto.CPDOResponse{
		Datadomain: datadomain,
	}, nil
}

// 协议组织
func (s *ProtocolServer) ProtocolOrganization(ctx context.Context, req *proto.CPORequest) (*proto.CPOResponse, error) {
	client, exists := s.gateway.GetRegistry().GetService(req.Name)
	if !exists {
		return nil, fmt.Errorf("service not found: %s", req.Name)
	}

	frame, err := client.ProtocolOrganization(ctx, req.Params)
	if err != nil {
		return nil, err
	}

	return &proto.CPOResponse{
		Frame: frame,
	}, nil
}

// 协议解析
func (s *ProtocolServer) ProtocolParsing(ctx context.Context, req *proto.CPPRequest) (*proto.CPPResponse, error) {
	client, exists := s.gateway.GetRegistry().GetService(req.Name)
	if !exists {
		return nil, fmt.Errorf("service not found: %s", req.Name)
	}

	data, err := client.ProtocolParsing(ctx, req.Frame)
	if err != nil {
		return nil, err
	}

	return &proto.CPPResponse{
		Data: data,
	}, nil
}

// 协议数据域解析
func (s *ProtocolServer) ProtocolDataDomainParsing(ctx context.Context, req *proto.CPDRequest) (*proto.CPDResponse, error) {
	client, exists := s.gateway.GetRegistry().GetService(req.Name)
	if !exists {
		return nil, fmt.Errorf("service not found: %s", req.Name)
	}

	data, err := client.ProtocolDataDomainParsing(ctx, req.Datadomain)
	if err != nil {
		return nil, err
	}

	return &proto.CPDResponse{
		Data: data,
	}, nil
}

// GetGateway 获取协议网关 (用于管理和监控)
func (s *ProtocolServer) GetGateway() *ProtocolGateway {
	return s.gateway
}
