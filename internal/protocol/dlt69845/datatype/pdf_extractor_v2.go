package datatype

import (
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/unidoc/unipdf/v3/extractor"
	"github.com/unidoc/unipdf/v3/model"
)

// PDFExtractorV2 改进的PDF文档内容提取器
type PDFExtractorV2 struct {
	pdfPath    string
	outputDir  string
	content    string
	structured map[string]interface{}
}

// NewPDFExtractorV2 创建新的PDF提取器
func NewPDFExtractorV2(pdfPath, outputDir string) *PDFExtractorV2 {
	return &PDFExtractorV2{
		pdfPath:    pdfPath,
		outputDir:  outputDir,
		structured: make(map[string]interface{}),
	}
}

// ExtractContent 提取PDF内容
func (pe *PDFExtractorV2) ExtractContent() error {
	// 打开PDF文件
	file, err := os.Open(pe.pdfPath)
	if err != nil {
		return fmt.Errorf("无法打开PDF文件: %v", err)
	}
	defer file.Close()

	// 创建PDF读取器
	pdfReader, err := model.NewPdfReader(file)
	if err != nil {
		return fmt.Errorf("无法创建PDF读取器: %v", err)
	}

	var contentBuilder strings.Builder
	
	// 获取页面数量
	numPages, err := pdfReader.GetNumPages()
	if err != nil {
		return fmt.Errorf("无法获取页面数量: %v", err)
	}

	// 逐页提取内容
	for pageNum := 1; pageNum <= numPages; pageNum++ {
		page, err := pdfReader.GetPage(pageNum)
		if err != nil {
			fmt.Printf("警告: 无法获取第%d页: %v\n", pageNum, err)
			continue
		}

		// 创建文本提取器
		textExtractor, err := extractor.New(page)
		if err != nil {
			fmt.Printf("警告: 无法创建第%d页的文本提取器: %v\n", pageNum, err)
			continue
		}

		// 提取页面文本
		pageText, err := textExtractor.ExtractText()
		if err != nil {
			fmt.Printf("警告: 无法提取第%d页内容: %v\n", pageNum, err)
			continue
		}

		contentBuilder.WriteString(fmt.Sprintf("\n=== 第 %d 页 ===\n", pageNum))
		contentBuilder.WriteString(pageText)
		contentBuilder.WriteString("\n")
	}

	pe.content = contentBuilder.String()
	return nil
}

// ParseAXDRStructures 解析A-XDR编码规则相关的数据结构
func (pe *PDFExtractorV2) ParseAXDRStructures() error {
	if pe.content == "" {
		return fmt.Errorf("请先提取PDF内容")
	}

	// 解析数据类型定义
	pe.parseDataTypes()
	
	// 解析编码规则
	pe.parseEncodingRules()
	
	// 解析结构定义
	pe.parseStructureDefinitions()

	return nil
}

// parseDataTypes 解析基本数据类型
func (pe *PDFExtractorV2) parseDataTypes() {
	dataTypes := make(map[string]string)
	
	// 匹配数据类型定义的正则表达式
	patterns := []string{
		`(?i)(boolean|bit-string|double-long|double-long-unsigned|enum|float32|float64|integer|long|long-unsigned|long64|long64-unsigned|octet-string|unsigned|visible-string|utf8-string|bcd|date|time|date-time|dont-care)\s*[:：]\s*([^\n\r]+)`,
		`(?i)(布尔|位串|双长整数|双长无符号整数|枚举|浮点32|浮点64|整数|长整数|长无符号整数|长64|长64无符号|八位串|无符号|可见字符串|UTF8字符串|BCD|日期|时间|日期时间|无关)\s*[:：]\s*([^\n\r]+)`,
		`(?i)数据类型\s*[:：]\s*([^\n\r]+)`,
		`(?i)类型\s*[:：]\s*([^\n\r]+)`,
	}
	
	for _, pattern := range patterns {
		typeRegex := regexp.MustCompile(pattern)
		matches := typeRegex.FindAllStringSubmatch(pe.content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				typeName := strings.TrimSpace(match[1])
				description := strings.TrimSpace(match[2])
				dataTypes[typeName] = description
			}
		}
	}
	
	pe.structured["dataTypes"] = dataTypes
}

// parseEncodingRules 解析编码规则
func (pe *PDFExtractorV2) parseEncodingRules() {
	encodingRules := make(map[string]string)
	
	// 匹配编码规则的正则表达式
	patterns := []string{
		`(?i)编码规则\s*[:：]\s*([^\n\r]+)`,
		`(?i)编码\s*[:：]\s*([^\n\r]+)`,
		`(?i)A-XDR\s*编码\s*[:：]\s*([^\n\r]+)`,
		`(?i)规则\s*[:：]\s*([^\n\r]+)`,
	}
	
	for _, pattern := range patterns {
		ruleRegex := regexp.MustCompile(pattern)
		matches := ruleRegex.FindAllStringSubmatch(pe.content, -1)
		for i, match := range matches {
			if len(match) >= 2 {
				ruleName := fmt.Sprintf("rule_%d", i+1)
				description := strings.TrimSpace(match[1])
				encodingRules[ruleName] = description
			}
		}
	}
	
	pe.structured["encodingRules"] = encodingRules
}

// parseStructureDefinitions 解析结构定义
func (pe *PDFExtractorV2) parseStructureDefinitions() {
	structures := make(map[string]interface{})
	
	// 匹配结构定义的正则表达式
	patterns := []string{
		`(?i)(structure|array|choice)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\{([^}]+)\}`,
		`(?i)(结构|数组|选择)\s+([a-zA-Z_\u4e00-\u9fa5][a-zA-Z0-9_\u4e00-\u9fa5]*)\s*\{([^}]+)\}`,
		`(?i)结构体\s*[:：]\s*([^\n\r]+)`,
		`(?i)数据结构\s*[:：]\s*([^\n\r]+)`,
	}
	
	for _, pattern := range patterns {
		structRegex := regexp.MustCompile(pattern)
		matches := structRegex.FindAllStringSubmatch(pe.content, -1)
		for _, match := range matches {
			if len(match) >= 4 {
				structType := strings.TrimSpace(match[1])
				structName := strings.TrimSpace(match[2])
				structBody := strings.TrimSpace(match[3])
				
				structures[structName] = map[string]interface{}{
					"type": structType,
					"body": structBody,
				}
			} else if len(match) >= 2 {
				structName := fmt.Sprintf("struct_%d", len(structures)+1)
				description := strings.TrimSpace(match[1])
				structures[structName] = map[string]interface{}{
					"type": "unknown",
					"body": description,
				}
			}
		}
	}
	
	pe.structured["structures"] = structures
}

// SaveToFiles 将提取的内容保存到文件
func (pe *PDFExtractorV2) SaveToFiles() error {
	// 确保输出目录存在
	if err := os.MkdirAll(pe.outputDir, 0755); err != nil {
		return fmt.Errorf("无法创建输出目录: %v", err)
	}

	// 保存原始内容
	rawContentPath := filepath.Join(pe.outputDir, "dlt790_6_raw_content_v2.txt")
	if err := os.WriteFile(rawContentPath, []byte(pe.content), 0644); err != nil {
		return fmt.Errorf("无法保存原始内容: %v", err)
	}

	// 保存数据类型定义
	if err := pe.saveDataTypes(); err != nil {
		return err
	}

	// 保存编码规则
	if err := pe.saveEncodingRules(); err != nil {
		return err
	}

	// 保存结构定义
	if err := pe.saveStructures(); err != nil {
		return err
	}

	return nil
}

// saveDataTypes 保存数据类型定义到Go文件
func (pe *PDFExtractorV2) saveDataTypes() error {
	dataTypes, ok := pe.structured["dataTypes"].(map[string]string)
	if !ok {
		return nil
	}

	var builder strings.Builder
	builder.WriteString("package datatype\n\n")
	builder.WriteString("// A-XDR基本数据类型定义 (从DLT 790.6-2010标准提取 - V2)\n\n")

	for typeName, description := range dataTypes {
		builder.WriteString(fmt.Sprintf("// %s %s\n", typeName, description))
		
		// 根据类型名生成Go类型定义
		goType := pe.mapToGoType(typeName)
		if goType != "" {
			builder.WriteString(fmt.Sprintf("type %s %s\n\n", 
				pe.toCamelCase(typeName), goType))
		}
	}

	filePath := filepath.Join(pe.outputDir, "axdr_types_v2.go")
	return os.WriteFile(filePath, []byte(builder.String()), 0644)
}

// saveEncodingRules 保存编码规则
func (pe *PDFExtractorV2) saveEncodingRules() error {
	rules, ok := pe.structured["encodingRules"].(map[string]string)
	if !ok {
		return nil
	}

	var builder strings.Builder
	builder.WriteString("package datatype\n\n")
	builder.WriteString("// A-XDR编码规则 (从DLT 790.6-2010标准提取 - V2)\n\n")

	for ruleName, description := range rules {
		builder.WriteString(fmt.Sprintf("// %s: %s\n", ruleName, description))
	}

	filePath := filepath.Join(pe.outputDir, "axdr_encoding_rules_v2.go")
	return os.WriteFile(filePath, []byte(builder.String()), 0644)
}

// saveStructures 保存结构定义
func (pe *PDFExtractorV2) saveStructures() error {
	structures, ok := pe.structured["structures"].(map[string]interface{})
	if !ok {
		return nil
	}

	var builder strings.Builder
	builder.WriteString("package datatype\n\n")
	builder.WriteString("// A-XDR结构定义 (从DLT 790.6-2010标准提取 - V2)\n\n")

	for structName, structInfo := range structures {
		if info, ok := structInfo.(map[string]interface{}); ok {
			structType := info["type"].(string)
			structBody := info["body"].(string)
			
			builder.WriteString(fmt.Sprintf("// %s %s结构\n", structName, structType))
			builder.WriteString(fmt.Sprintf("// 原始定义: %s\n", structBody))
			builder.WriteString(fmt.Sprintf("type %s struct {\n", pe.toCamelCase(structName)))
			builder.WriteString("    // TODO: 根据标准文档完善字段定义\n")
			builder.WriteString("}\n\n")
		}
	}

	filePath := filepath.Join(pe.outputDir, "axdr_structures_v2.go")
	return os.WriteFile(filePath, []byte(builder.String()), 0644)
}

// mapToGoType 将A-XDR类型映射到Go类型
func (pe *PDFExtractorV2) mapToGoType(axdrType string) string {
	typeMap := map[string]string{
		"boolean":             "bool",
		"bit-string":          "[]byte",
		"double-long":         "int32",
		"double-long-unsigned": "uint32",
		"enum":                "int",
		"float32":             "float32",
		"float64":             "float64",
		"integer":             "int8",
		"long":                "int16",
		"long-unsigned":       "uint16",
		"long64":              "int64",
		"long64-unsigned":     "uint64",
		"octet-string":        "[]byte",
		"unsigned":            "uint8",
		"visible-string":      "string",
		"utf8-string":         "string",
		"bcd":                 "[]byte",
		"date":                "[]byte",
		"time":                "[]byte",
		"date-time":           "[]byte",
		"dont-care":           "interface{}",
		// 中文类型映射
		"布尔":                  "bool",
		"位串":                  "[]byte",
		"双长整数":               "int32",
		"双长无符号整数":          "uint32",
		"枚举":                  "int",
		"浮点32":               "float32",
		"浮点64":               "float64",
		"整数":                  "int8",
		"长整数":                "int16",
		"长无符号整数":           "uint16",
		"长64":                 "int64",
		"长64无符号":            "uint64",
		"八位串":                "[]byte",
		"无符号":                "uint8",
		"可见字符串":             "string",
		"UTF8字符串":           "string",
		"BCD":                 "[]byte",
		"日期":                  "[]byte",
		"时间":                  "[]byte",
		"日期时间":               "[]byte",
		"无关":                  "interface{}",
	}
	
	return typeMap[strings.ToLower(axdrType)]
}

// toCamelCase 转换为驼峰命名
func (pe *PDFExtractorV2) toCamelCase(s string) string {
	parts := strings.FieldsFunc(s, func(r rune) bool {
		return r == '-' || r == '_' || r == ' '
	})
	
	for i, part := range parts {
		if len(part) > 0 {
			parts[i] = strings.ToUpper(part[:1]) + strings.ToLower(part[1:])
		}
	}
	
	return strings.Join(parts, "")
}

// GetContent 获取提取的内容
func (pe *PDFExtractorV2) GetContent() string {
	return pe.content
}

// GetStructuredData 获取结构化数据
func (pe *PDFExtractorV2) GetStructuredData() map[string]interface{} {
	return pe.structured
}
