package datatype

import "fmt"

// A-XDR基本数据类型定义 (根据DLT 790.6-2010标准手动定义)
// 参考: DL T 790.6-2010 采用配电线载波的配电自动化 第6部分: A-XDR编码规则

// Boolean 布尔类型
// 编码: 1字节，0x00表示false，0x01表示true
type Boolean bool

// BitString 位串类型
// 编码: 长度字节 + 数据字节
type BitString struct {
	Length uint8  // 位长度
	Data   []byte // 数据
}

// Integer 整数类型 (8位有符号)
// 编码: 1字节，二进制补码表示
type Integer int8

// Unsigned 无符号整数类型 (8位无符号)
// 编码: 1字节
type Unsigned uint8

// Long 长整数类型 (16位有符号)
// 编码: 2字节，大端序，二进制补码表示
type Long int16

// LongUnsigned 长无符号整数类型 (16位无符号)
// 编码: 2字节，大端序
type LongUnsigned uint16

// DoubleLong 双长整数类型 (32位有符号)
// 编码: 4字节，大端序，二进制补码表示
type DoubleLong int32

// DoubleLongUnsigned 双长无符号整数类型 (32位无符号)
// 编码: 4字节，大端序
type DoubleLongUnsigned uint32

// Long64 64位长整数类型 (64位有符号)
// 编码: 8字节，大端序，二进制补码表示
type Long64 int64

// Long64Unsigned 64位长无符号整数类型 (64位无符号)
// 编码: 8字节，大端序
type Long64Unsigned uint64

// Float32 32位浮点数类型
// 编码: 4字节，IEEE 754格式，大端序
type Float32 float32

// Float64 64位浮点数类型
// 编码: 8字节，IEEE 754格式，大端序
type Float64 float64

// OctetString 八位字节串类型
// 编码: 长度字节 + 数据字节
type OctetString struct {
	Length uint8  // 字节长度
	Data   []byte // 数据
}

// VisibleString 可见字符串类型
// 编码: 长度字节 + ASCII字符数据
type VisibleString struct {
	Length uint8  // 字符长度
	Data   string // ASCII字符串
}

// UTF8String UTF-8字符串类型
// 编码: 长度字节 + UTF-8编码数据
type UTF8String struct {
	Length uint8  // 字节长度
	Data   string // UTF-8字符串
}

// BCD BCD编码类型
// 编码: 每个字节包含两个BCD数字
type BCD []byte

// Date 日期类型
// 编码: 12字节，年(2字节) + 月(1字节) + 日(1字节) + 星期(1字节) + 小时(1字节) + 分钟(1字节) + 秒(1字节) + 百分之一秒(1字节) + 偏差(2字节) + 时钟状态(1字节)
type Date struct {
	Year        uint16 // 年份
	Month       uint8  // 月份 (1-12)
	Day         uint8  // 日期 (1-31)
	DayOfWeek   uint8  // 星期 (1-7, 1=星期一)
	Hour        uint8  // 小时 (0-23)
	Minute      uint8  // 分钟 (0-59)
	Second      uint8  // 秒 (0-59)
	Hundredths  uint8  // 百分之一秒 (0-99)
	Deviation   int16  // 时区偏差(分钟)
	ClockStatus uint8  // 时钟状态
}

// Time 时间类型
// 编码: 4字节，小时(1字节) + 分钟(1字节) + 秒(1字节) + 百分之一秒(1字节)
type Time struct {
	Hour       uint8 // 小时 (0-23)
	Minute     uint8 // 分钟 (0-59)
	Second     uint8 // 秒 (0-59)
	Hundredths uint8 // 百分之一秒 (0-99)
}

// DateTime 日期时间类型
// 编码: 12字节，与Date相同
type DateTime Date

// Enum 枚举类型
// 编码: 1字节，枚举值
type Enum uint8

// Array 数组类型
// 编码: 数组长度 + 元素数据
type Array struct {
	Length   uint16      // 数组长度
	Elements interface{} // 数组元素
}

// Structure 结构类型
// 编码: 结构字段按顺序编码
type Structure struct {
	Fields []interface{} // 结构字段
}

// Choice 选择类型
// 编码: 选择标识 + 选择的数据
type Choice struct {
	Tag  uint8       // 选择标识
	Data interface{} // 选择的数据
}

// NullData 空数据类型
// 编码: 0字节
type NullData struct{}

// DontCare 无关类型
// 编码: 可变长度
type DontCare interface{}

// CompactArray 紧凑数组类型
// 编码: 数组长度(可变长度编码) + 元素数据
type CompactArray struct {
	Length   interface{} // 可变长度编码的数组长度
	Elements interface{} // 数组元素
}

// 常用的A-XDR编码常量
const (
	// 布尔值常量
	BooleanFalse = 0x00
	BooleanTrue  = 0x01

	// 空值标识
	NullValue = 0x00

	// 数组长度编码标识
	ArrayLengthByte  = 0x01 // 1字节长度
	ArrayLengthWord  = 0x02 // 2字节长度
	ArrayLengthDWord = 0x04 // 4字节长度
	ArrayLengthQWord = 0x08 // 8字节长度

	// 选择类型标识
	ChoiceTag0  = 0x00
	ChoiceTag1  = 0x01
	ChoiceTag2  = 0x02
	ChoiceTag3  = 0x03
	ChoiceTag4  = 0x04
	ChoiceTag5  = 0x05
	ChoiceTag6  = 0x06
	ChoiceTag7  = 0x07
	ChoiceTag8  = 0x08
	ChoiceTag9  = 0x09
	ChoiceTag10 = 0x0A
	ChoiceTag11 = 0x0B
	ChoiceTag12 = 0x0C
	ChoiceTag13 = 0x0D
	ChoiceTag14 = 0x0E
	ChoiceTag15 = 0x0F

	// 时钟状态位定义
	ClockStatusInvalid        = 0x01 // 无效时间
	ClockStatusDoubtful       = 0x02 // 可疑时间
	ClockStatusDifferentBase  = 0x04 // 不同的时间基准
	ClockStatusInvalidStatus  = 0x08 // 无效的状态
	ClockStatusDaylightSaving = 0x80 // 夏令时
)

// A-XDR编码规则接口
type AXDREncoder interface {
	Encode() ([]byte, error)
}

// A-XDR解码规则接口
type AXDRDecoder interface {
	Decode(data []byte) error
}

// A-XDR编解码器接口
type AXDRCodec interface {
	AXDREncoder
	AXDRDecoder
}

// 长度编码函数类型
type LengthEncoder func(length uint64) []byte

// 长度解码函数类型
type LengthDecoder func(data []byte) (length uint64, consumed int, err error)

// 可变长度编码规则
// 根据DLT 790.6-2010标准，长度编码规则如下:
// - 如果长度 <= 127，使用1字节编码
// - 如果长度 > 127，第一字节的高位为1，低7位表示后续字节数，然后是实际长度值
func EncodeLength(length uint64) []byte {
	if length <= 127 {
		return []byte{byte(length)}
	}

	// 计算需要多少字节来表示长度
	var bytes []byte
	temp := length
	for temp > 0 {
		bytes = append([]byte{byte(temp & 0xFF)}, bytes...)
		temp >>= 8
	}

	// 第一字节: 0x80 | 字节数
	result := []byte{0x80 | byte(len(bytes))}
	result = append(result, bytes...)
	return result
}

// 解码可变长度
func DecodeLength(data []byte) (length uint64, consumed int, err error) {
	if len(data) == 0 {
		return 0, 0, fmt.Errorf("数据为空")
	}

	firstByte := data[0]
	if firstByte&0x80 == 0 {
		// 短格式: 长度 <= 127
		return uint64(firstByte), 1, nil
	}

	// 长格式: 长度 > 127
	lengthBytes := int(firstByte & 0x7F)
	if lengthBytes == 0 {
		return 0, 1, fmt.Errorf("无效的长度编码")
	}

	if len(data) < 1+lengthBytes {
		return 0, 0, fmt.Errorf("数据不足")
	}

	length = 0
	for i := 1; i <= lengthBytes; i++ {
		length = (length << 8) | uint64(data[i])
	}

	return length, 1 + lengthBytes, nil
}

// 错误定义
var (
	ErrInvalidData   = fmt.Errorf("无效数据")
	ErrDataTooShort  = fmt.Errorf("数据太短")
	ErrInvalidLength = fmt.Errorf("无效长度")
	ErrInvalidType   = fmt.Errorf("无效类型")
)
