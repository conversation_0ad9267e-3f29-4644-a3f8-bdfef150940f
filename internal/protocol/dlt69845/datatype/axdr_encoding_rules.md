# A-XDR编码规则说明

基于 DL/T 790.6-2010《采用配电线载波的配电自动化 第6部分: A-XDR编码规则》标准

## 1. 概述

A-XDR (Abstract Syntax Notation One - eXternal Data Representation) 是一种数据编码规则，用于在不同系统之间传输结构化数据。本文档描述了DLT 790.6-2010标准中定义的A-XDR编码规则。

## 2. 基本编码原则

### 2.1 字节序
- 所有多字节数据采用**大端序**（Big-Endian）编码
- 最高有效字节在前，最低有效字节在后

### 2.2 对齐方式
- 数据按字节对齐，不需要特殊的字节边界对齐

### 2.3 长度编码
- 可变长度数据使用长度前缀编码
- 长度编码规则：
  - 如果长度 ≤ 127：使用1字节直接编码
  - 如果长度 > 127：使用多字节编码
    - 第1字节：0x80 | 后续字节数
    - 后续字节：实际长度值（大端序）

## 3. 基本数据类型编码

### 3.1 Boolean (布尔类型)
- **长度**: 1字节
- **编码**: 
  - `0x00` = false
  - `0x01` = true
- **示例**: 
  - true → `01`
  - false → `00`

### 3.2 Integer (8位有符号整数)
- **长度**: 1字节
- **编码**: 二进制补码表示
- **范围**: -128 ~ 127
- **示例**:
  - 100 → `64`
  - -50 → `CE`

### 3.3 Unsigned (8位无符号整数)
- **长度**: 1字节
- **编码**: 直接二进制表示
- **范围**: 0 ~ 255
- **示例**:
  - 200 → `C8`
  - 0 → `00`

### 3.4 Long (16位有符号整数)
- **长度**: 2字节
- **编码**: 大端序，二进制补码表示
- **范围**: -32768 ~ 32767
- **示例**:
  - 1000 → `03 E8`
  - -1000 → `FC 18`

### 3.5 Long-Unsigned (16位无符号整数)
- **长度**: 2字节
- **编码**: 大端序
- **范围**: 0 ~ 65535
- **示例**:
  - 50000 → `C3 50`
  - 256 → `01 00`

### 3.6 Double-Long (32位有符号整数)
- **长度**: 4字节
- **编码**: 大端序，二进制补码表示
- **范围**: -2147483648 ~ 2147483647
- **示例**:
  - 100000 → `00 01 86 A0`
  - -100000 → `FF FE 79 60`

### 3.7 Double-Long-Unsigned (32位无符号整数)
- **长度**: 4字节
- **编码**: 大端序
- **范围**: 0 ~ 4294967295
- **示例**:
  - 3000000000 → `B2 D0 5E 00`

### 3.8 Long64 (64位有符号整数)
- **长度**: 8字节
- **编码**: 大端序，二进制补码表示
- **示例**:
  - 1000000000000 → `00 00 00 E8 D4 A5 10 00`

### 3.9 Long64-Unsigned (64位无符号整数)
- **长度**: 8字节
- **编码**: 大端序
- **示例**:
  - 18446744073709551615 → `FF FF FF FF FF FF FF FF`

### 3.10 Float32 (32位浮点数)
- **长度**: 4字节
- **编码**: IEEE 754格式，大端序
- **示例**:
  - 3.14159 → `40 49 0F DB`

### 3.11 Float64 (64位浮点数)
- **长度**: 8字节
- **编码**: IEEE 754格式，大端序
- **示例**:
  - 3.141592653589793 → `40 09 21 FB 54 44 2D 18`

## 4. 复合数据类型编码

### 4.1 Bit-String (位串)
- **编码**: 长度字节 + 数据字节
- **长度字节**: 位的数量
- **数据字节**: 位数据，不足8位的用0填充
- **示例**:
  - 5位 "10110" → `05 B0` (长度5，数据10110000)

### 4.2 Octet-String (八位字节串)
- **编码**: 长度编码 + 数据字节
- **示例**:
  - "Hello" → `05 48 65 6C 6C 6F`

### 4.3 Visible-String (可见字符串)
- **编码**: 长度编码 + ASCII字符数据
- **字符集**: ASCII可打印字符 (0x20-0x7E)
- **示例**:
  - "ABC" → `03 41 42 43`

### 4.4 UTF8-String (UTF-8字符串)
- **编码**: 长度编码 + UTF-8编码数据
- **长度**: UTF-8编码后的字节数
- **示例**:
  - "中文" → `06 E4 B8 AD E6 96 87`

### 4.5 BCD (二进制编码十进制)
- **编码**: 每个字节包含两个BCD数字
- **格式**: 高4位为十位，低4位为个位
- **示例**:
  - 1234 → `12 34`
  - 567 → `05 67` (奇数位数前补0)

## 5. 时间日期类型编码

### 5.1 Date (日期)
- **长度**: 12字节
- **格式**: 年(2) + 月(1) + 日(1) + 星期(1) + 时(1) + 分(1) + 秒(1) + 百分秒(1) + 时区偏差(2) + 时钟状态(1)
- **示例**: 2024-06-28 14:30:25.50 周五 UTC+8
  ```
  07 E8  // 年份 2024
  06     // 月份 6
  1C     // 日期 28
  05     // 星期五
  0E     // 小时 14
  1E     // 分钟 30
  19     // 秒 25
  32     // 百分之一秒 50
  01 E0  // 时区偏差 +480分钟 (UTC+8)
  00     // 时钟状态 正常
  ```

### 5.2 Time (时间)
- **长度**: 4字节
- **格式**: 时(1) + 分(1) + 秒(1) + 百分秒(1)
- **示例**: 14:30:25.50 → `0E 1E 19 32`

### 5.3 Date-Time (日期时间)
- **编码**: 与Date相同，12字节

## 6. 结构化数据类型编码

### 6.1 Array (数组)
- **编码**: 数组长度编码 + 元素数据
- **长度**: 数组元素个数
- **示例**: [1, 2, 3] (Integer数组)
  ```
  03     // 数组长度 3
  01     // 元素1: 1
  02     // 元素2: 2
  03     // 元素3: 3
  ```

### 6.2 Structure (结构)
- **编码**: 按字段顺序依次编码各字段
- **无长度前缀**: 结构长度由字段定义确定
- **示例**: {name: "ABC", age: 25}
  ```
  03 41 42 43  // name字段: "ABC"
  19           // age字段: 25
  ```

### 6.3 Choice (选择)
- **编码**: 选择标识 + 选择的数据
- **选择标识**: 1字节，表示选择的分支
- **示例**: Choice选择第2个分支，值为100
  ```
  01     // 选择标识 1 (第2个分支，从0开始)
  64     // 选择的值: 100
  ```

### 6.4 Compact-Array (紧凑数组)
- **编码**: 可变长度编码的数组长度 + 元素数据
- **用于**: 大型数组的高效编码
- **示例**: 包含200个元素的数组
  ```
  81 C8  // 长度编码: 200 (使用2字节编码)
  ...    // 200个元素的数据
  ```

## 7. 特殊类型编码

### 7.1 Null-Data (空数据)
- **长度**: 0字节
- **编码**: 无数据
- **用途**: 表示空值或占位符

### 7.2 Enum (枚举)
- **长度**: 1字节
- **编码**: 枚举值的索引
- **示例**: 枚举值 {RED=0, GREEN=1, BLUE=2}，选择GREEN → `01`

### 7.3 Dont-Care (无关)
- **编码**: 可变长度，具体格式由应用定义
- **用途**: 应用特定的数据类型

## 8. 编码示例

### 8.1 复杂结构示例
```go
// 定义结构
type DeviceInfo struct {
    ID       Long-Unsigned    // 设备ID
    Name     Visible-String   // 设备名称
    Status   Boolean          // 状态
    Values   Array            // 数值数组
}

// 示例数据
DeviceInfo {
    ID: 1001,
    Name: "Meter01",
    Status: true,
    Values: [100, 200, 300]
}

// 编码结果
03 E9              // ID: 1001
07 4D 65 74 65 72 30 31  // Name: "Meter01"
01                 // Status: true
03                 // Values数组长度: 3
00 64              // Values[0]: 100
00 C8              // Values[1]: 200
01 2C              // Values[2]: 300
```

## 9. 错误处理

### 9.1 常见错误
- **数据长度不足**: 解码时数据不够
- **无效长度编码**: 长度编码格式错误
- **类型不匹配**: 数据类型与期望不符
- **范围溢出**: 数值超出类型范围

### 9.2 错误码定义
- `0x01`: 数据长度不足
- `0x02`: 无效的长度编码
- `0x03`: 类型不匹配
- `0x04`: 数值范围溢出
- `0x05`: 无效的数据格式

## 10. 实现注意事项

### 10.1 性能优化
- 使用字节缓冲区减少内存分配
- 预计算常用数据的编码结果
- 批量处理数组数据

### 10.2 兼容性
- 严格按照标准实现编码规则
- 处理不同版本间的兼容性问题
- 提供向后兼容的解码能力

### 10.3 调试支持
- 提供详细的编码/解码日志
- 支持十六进制数据的可视化
- 实现数据完整性校验

## 11. 参考资料

- DL/T 790.6-2010《采用配电线载波的配电自动化 第6部分: A-XDR编码规则》
- IEEE 754 浮点数标准
- UTF-8 编码标准
- ASCII 字符集标准
