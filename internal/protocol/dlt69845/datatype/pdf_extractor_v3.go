package datatype

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
)

// PDFExtractorV3 使用外部工具的PDF文档内容提取器
type PDFExtractorV3 struct {
	pdfPath    string
	outputDir  string
	content    string
	structured map[string]interface{}
}

// NewPDFExtractorV3 创建新的PDF提取器V3
func NewPDFExtractorV3(pdfPath, outputDir string) *PDFExtractorV3 {
	return &PDFExtractorV3{
		pdfPath:    pdfPath,
		outputDir:  outputDir,
		structured: make(map[string]interface{}),
	}
}

// ExtractContent 使用pdftotext工具提取PDF内容
func (pe *PDFExtractorV3) ExtractContent() error {
	// 检查pdftotext是否可用
	if _, err := exec.LookPath("pdftotext"); err != nil {
		return pe.extractWithPython()
	}

	// 使用pdftotext提取内容
	tempFile := filepath.Join(os.TempDir(), "extracted_content.txt")
	defer os.Remove(tempFile)

	cmd := exec.Command("pdftotext", "-layout", "-enc", "UTF-8", pe.pdfPath, tempFile)
	if err := cmd.Run(); err != nil {
		return pe.extractWithPython()
	}

	// 读取提取的内容
	content, err := os.ReadFile(tempFile)
	if err != nil {
		return fmt.Errorf("无法读取提取的内容: %v", err)
	}

	pe.content = string(content)
	return nil
}

// extractWithPython 使用Python脚本提取PDF内容
func (pe *PDFExtractorV3) extractWithPython() error {
	// 创建临时Python脚本
	pythonScript := `
import sys
import os

try:
    import PyPDF2
except ImportError:
    try:
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyPDF2"])
        import PyPDF2
    except:
        print("无法安装PyPDF2，尝试使用pdfplumber")
        try:
            import pdfplumber
        except ImportError:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pdfplumber"])
            import pdfplumber

def extract_with_pypdf2(pdf_path):
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page_num, page in enumerate(reader.pages):
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                text += page.extract_text()
                text += "\n"
            return text
    except Exception as e:
        print(f"PyPDF2提取失败: {e}")
        return None

def extract_with_pdfplumber(pdf_path):
    try:
        import pdfplumber
        text = ""
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                text += f"\n=== 第 {page_num + 1} 页 ===\n"
                page_text = page.extract_text()
                if page_text:
                    text += page_text
                text += "\n"
        return text
    except Exception as e:
        print(f"pdfplumber提取失败: {e}")
        return None

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("用法: python script.py <pdf_path> <output_path>")
        sys.exit(1)

    pdf_path = sys.argv[1]
    output_path = sys.argv[2]

    # 尝试PyPDF2
    text = extract_with_pypdf2(pdf_path)

    # 如果PyPDF2失败，尝试pdfplumber
    if not text:
        text = extract_with_pdfplumber(pdf_path)

    if text:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"成功提取PDF内容到: {output_path}")
    else:
        print("所有PDF提取方法都失败了")
        sys.exit(1)
`

	// 创建临时Python脚本文件
	scriptPath := filepath.Join(os.TempDir(), "pdf_extractor.py")
	if err := os.WriteFile(scriptPath, []byte(pythonScript), 0644); err != nil {
		return fmt.Errorf("无法创建Python脚本: %v", err)
	}
	defer os.Remove(scriptPath)

	// 创建临时输出文件
	tempFile := filepath.Join(os.TempDir(), "extracted_content.txt")
	defer os.Remove(tempFile)

	// 运行Python脚本
	cmd := exec.Command("python3", scriptPath, pe.pdfPath, tempFile)
	output, err := cmd.CombinedOutput()
	if err != nil {
		// 尝试python而不是python3
		cmd = exec.Command("python", scriptPath, pe.pdfPath, tempFile)
		output, err = cmd.CombinedOutput()
		if err != nil {
			return fmt.Errorf("Python提取失败: %v\n输出: %s", err, string(output))
		}
	}

	// 读取提取的内容
	content, err := os.ReadFile(tempFile)
	if err != nil {
		return fmt.Errorf("无法读取提取的内容: %v", err)
	}

	pe.content = string(content)
	fmt.Printf("Python提取输出: %s\n", string(output))
	return nil
}

// ParseAXDRStructures 解析A-XDR编码规则相关的数据结构
func (pe *PDFExtractorV3) ParseAXDRStructures() error {
	if pe.content == "" {
		return fmt.Errorf("请先提取PDF内容")
	}

	// 解析数据类型定义
	pe.parseDataTypes()

	// 解析编码规则
	pe.parseEncodingRules()

	// 解析结构定义
	pe.parseStructureDefinitions()

	// 解析表格数据
	pe.parseTableData()

	return nil
}

// parseDataTypes 解析基本数据类型
func (pe *PDFExtractorV3) parseDataTypes() {
	dataTypes := make(map[string]string)

	// 更全面的数据类型匹配模式
	patterns := []string{
		// 中文模式
		`(?i)(布尔|位串|双长整数|双长无符号|枚举|浮点32|浮点64|整数|长整数|长无符号|长64|长64无符号|八位字节串|无符号|可见字符串|UTF8字符串|BCD|日期|时间|日期时间|无关)\s*[:：]\s*([^\n\r]+)`,
		// 英文模式
		`(?i)(boolean|bit-string|double-long|double-long-unsigned|enum|float32|float64|integer|long|long-unsigned|long64|long64-unsigned|octet-string|unsigned|visible-string|utf8-string|bcd|date|time|date-time|dont-care)\s*[:：]\s*([^\n\r]+)`,
		// 表格模式
		`(?i)(boolean|bit-string|double-long|double-long-unsigned|enum|float32|float64|integer|long|long-unsigned|long64|long64-unsigned|octet-string|unsigned|visible-string|utf8-string|bcd|date|time|date-time|dont-care)\s+([^\n\r\t]+)\s+([^\n\r\t]+)`,
	}

	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		matches := regex.FindAllStringSubmatch(pe.content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				typeName := strings.TrimSpace(match[1])
				description := strings.TrimSpace(match[2])
				if len(match) >= 4 {
					description += " " + strings.TrimSpace(match[3])
				}
				dataTypes[typeName] = description
			}
		}
	}

	pe.structured["dataTypes"] = dataTypes
}

// parseEncodingRules 解析编码规则
func (pe *PDFExtractorV3) parseEncodingRules() {
	encodingRules := make(map[string]string)

	// 编码规则匹配模式
	patterns := []string{
		// 中文编码规则模式
		`(?i)编码规则\s*[:：]\s*([^\n\r]+)`,
		`(?i)编码\s*[:：]\s*([^\n\r]+)`,
		`(?i)表示方法\s*[:：]\s*([^\n\r]+)`,
		// 英文编码规则模式
		`(?i)encoding\s*[:：]\s*([^\n\r]+)`,
		`(?i)representation\s*[:：]\s*([^\n\r]+)`,
		// 表格中的编码规则
		`(?i)(编码|encoding)\s+([^\n\r\t]+)\s+([^\n\r\t]+)`,
	}

	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		matches := regex.FindAllStringSubmatch(pe.content, -1)
		for i, match := range matches {
			if len(match) >= 2 {
				ruleName := fmt.Sprintf("rule_%d", i+1)
				description := strings.TrimSpace(match[1])
				if len(match) >= 3 {
					description += " " + strings.TrimSpace(match[2])
				}
				encodingRules[ruleName] = description
			}
		}
	}

	pe.structured["encodingRules"] = encodingRules
}

// parseStructureDefinitions 解析结构定义
func (pe *PDFExtractorV3) parseStructureDefinitions() {
	structures := make(map[string]interface{})

	// 结构定义匹配模式
	patterns := []string{
		// 结构体定义
		`(?i)(structure|array|choice)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\{([^}]+)\}`,
		// 表格中的结构定义
		`(?i)(结构|structure)\s+([^\n\r\t]+)\s+([^\n\r\t]+)`,
		// 数组定义
		`(?i)(数组|array)\s+([^\n\r\t]+)\s+([^\n\r\t]+)`,
		// 选择定义
		`(?i)(选择|choice)\s+([^\n\r\t]+)\s+([^\n\r\t]+)`,
	}

	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		matches := regex.FindAllStringSubmatch(pe.content, -1)
		for _, match := range matches {
			if len(match) >= 4 {
				structType := strings.TrimSpace(match[1])
				structName := strings.TrimSpace(match[2])
				structBody := strings.TrimSpace(match[3])

				structures[structName] = map[string]interface{}{
					"type": structType,
					"body": structBody,
				}
			} else if len(match) >= 3 {
				structType := strings.TrimSpace(match[1])
				structName := strings.TrimSpace(match[2])

				structures[structName] = map[string]interface{}{
					"type": structType,
					"body": "",
				}
			}
		}
	}

	pe.structured["structures"] = structures
}

// parseTableData 解析表格数据
func (pe *PDFExtractorV3) parseTableData() {
	tables := make(map[string]interface{})

	// 查找表格标题和内容
	patterns := []string{
		// 表格标题模式
		`(?i)表\s*(\d+)\s*[-–—]\s*([^\n\r]+)`,
		`(?i)table\s*(\d+)\s*[-–—]\s*([^\n\r]+)`,
		// 数据类型表格
		`(?i)(数据类型|data\s*type)\s+([^\n\r]+)`,
		// 编码表格
		`(?i)(编码|encoding)\s+([^\n\r]+)`,
	}

	for _, pattern := range patterns {
		regex := regexp.MustCompile(pattern)
		matches := regex.FindAllStringSubmatch(pe.content, -1)
		for _, match := range matches {
			if len(match) >= 3 {
				tableNum := strings.TrimSpace(match[1])
				tableTitle := strings.TrimSpace(match[2])

				tables[fmt.Sprintf("table_%s", tableNum)] = map[string]interface{}{
					"title":   tableTitle,
					"content": "", // 这里可以进一步解析表格内容
				}
			}
		}
	}

	pe.structured["tables"] = tables
}

// SaveToFiles 将提取的内容保存到文件
func (pe *PDFExtractorV3) SaveToFiles() error {
	// 确保输出目录存在
	if err := os.MkdirAll(pe.outputDir, 0755); err != nil {
		return fmt.Errorf("无法创建输出目录: %v", err)
	}

	// 保存原始内容
	rawContentPath := filepath.Join(pe.outputDir, "dlt790_6_raw_content_v3.txt")
	if err := os.WriteFile(rawContentPath, []byte(pe.content), 0644); err != nil {
		return fmt.Errorf("无法保存原始内容: %v", err)
	}

	// 保存数据类型定义
	if err := pe.saveDataTypes(); err != nil {
		return err
	}

	// 保存编码规则
	if err := pe.saveEncodingRules(); err != nil {
		return err
	}

	// 保存结构定义
	if err := pe.saveStructures(); err != nil {
		return err
	}

	// 保存表格数据
	if err := pe.saveTables(); err != nil {
		return err
	}

	return nil
}

// saveDataTypes 保存数据类型定义到Go文件
func (pe *PDFExtractorV3) saveDataTypes() error {
	dataTypes, ok := pe.structured["dataTypes"].(map[string]string)
	if !ok {
		return nil
	}

	var builder strings.Builder
	builder.WriteString("package datatype\n\n")
	builder.WriteString("// A-XDR基本数据类型定义 (从DLT 790.6-2010标准提取 - V3版本)\n")
	builder.WriteString("// 提取时间: " + fmt.Sprintf("%v", os.Getenv("USER")) + "\n\n")

	// 添加导入语句
	builder.WriteString("import (\n")
	builder.WriteString("\t\"fmt\"\n")
	builder.WriteString("\t\"time\"\n")
	builder.WriteString(")\n\n")

	for typeName, description := range dataTypes {
		builder.WriteString(fmt.Sprintf("// %s %s\n", typeName, description))

		// 根据类型名生成Go类型定义
		goType := pe.mapToGoType(typeName)
		if goType != "" {
			camelName := pe.toCamelCase(typeName)
			builder.WriteString(fmt.Sprintf("type %s %s\n\n", camelName, goType))

			// 为某些类型添加方法
			pe.addTypeMethods(&builder, camelName, typeName, goType)
		}
	}

	filePath := filepath.Join(pe.outputDir, "axdr_types_v3.go")
	return os.WriteFile(filePath, []byte(builder.String()), 0644)
}

// addTypeMethods 为类型添加方法
func (pe *PDFExtractorV3) addTypeMethods(builder *strings.Builder, camelName, typeName, goType string) {
	switch strings.ToLower(typeName) {
	case "date", "time", "date-time":
		builder.WriteString(fmt.Sprintf("// String 返回%s的字符串表示\n", camelName))
		builder.WriteString(fmt.Sprintf("func (d %s) String() string {\n", camelName))
		builder.WriteString("\treturn fmt.Sprintf(\"%v\", []byte(d))\n")
		builder.WriteString("}\n\n")
	case "bcd":
		builder.WriteString(fmt.Sprintf("// String 返回%s的字符串表示\n", camelName))
		builder.WriteString(fmt.Sprintf("func (b %s) String() string {\n", camelName))
		builder.WriteString("\tresult := \"\"\n")
		builder.WriteString("\tfor _, byte := range b {\n")
		builder.WriteString("\t\thigh := (byte >> 4) & 0x0F\n")
		builder.WriteString("\t\tlow := byte & 0x0F\n")
		builder.WriteString("\t\tresult += fmt.Sprintf(\"%d%d\", high, low)\n")
		builder.WriteString("\t}\n")
		builder.WriteString("\treturn result\n")
		builder.WriteString("}\n\n")
	}
}

// saveEncodingRules 保存编码规则
func (pe *PDFExtractorV3) saveEncodingRules() error {
	rules, ok := pe.structured["encodingRules"].(map[string]string)
	if !ok {
		return nil
	}

	var builder strings.Builder
	builder.WriteString("package datatype\n\n")
	builder.WriteString("// A-XDR编码规则 (从DLT 790.6-2010标准提取 - V3版本)\n\n")

	for ruleName, description := range rules {
		builder.WriteString(fmt.Sprintf("// %s: %s\n", ruleName, description))
	}

	// 添加编码规则常量
	builder.WriteString("\n// A-XDR编码规则常量\n")
	builder.WriteString("const (\n")
	builder.WriteString("\t// 字节序\n")
	builder.WriteString("\tBigEndian = true // A-XDR使用大端序\n\n")
	builder.WriteString("\t// 长度编码阈值\n")
	builder.WriteString("\tShortLengthMax = 127 // 短格式长度最大值\n\n")
	builder.WriteString("\t// 编码标识\n")
	builder.WriteString("\tLongLengthFlag = 0x80 // 长格式长度标识\n")
	builder.WriteString(")\n\n")

	// 添加编码规则函数
	builder.WriteString("// IsShortLength 判断是否为短格式长度编码\n")
	builder.WriteString("func IsShortLength(length uint64) bool {\n")
	builder.WriteString("\treturn length <= ShortLengthMax\n")
	builder.WriteString("}\n\n")

	builder.WriteString("// GetLengthEncodingSize 获取长度编码所需字节数\n")
	builder.WriteString("func GetLengthEncodingSize(length uint64) int {\n")
	builder.WriteString("\tif IsShortLength(length) {\n")
	builder.WriteString("\t\treturn 1\n")
	builder.WriteString("\t}\n")
	builder.WriteString("\t// 计算需要多少字节表示长度值\n")
	builder.WriteString("\tbytes := 0\n")
	builder.WriteString("\ttemp := length\n")
	builder.WriteString("\tfor temp > 0 {\n")
	builder.WriteString("\t\tbytes++\n")
	builder.WriteString("\t\ttemp >>= 8\n")
	builder.WriteString("\t}\n")
	builder.WriteString("\treturn 1 + bytes // 1字节标识 + 长度字节\n")
	builder.WriteString("}\n")

	filePath := filepath.Join(pe.outputDir, "axdr_encoding_rules_v3.go")
	return os.WriteFile(filePath, []byte(builder.String()), 0644)
}

// saveStructures 保存结构定义
func (pe *PDFExtractorV3) saveStructures() error {
	structures, ok := pe.structured["structures"].(map[string]interface{})
	if !ok {
		return nil
	}

	var builder strings.Builder
	builder.WriteString("package datatype\n\n")
	builder.WriteString("// A-XDR结构定义 (从DLT 790.6-2010标准提取 - V3版本)\n\n")

	for structName, structInfo := range structures {
		if info, ok := structInfo.(map[string]interface{}); ok {
			structType := info["type"].(string)
			structBody := info["body"].(string)

			builder.WriteString(fmt.Sprintf("// %s %s结构\n", structName, structType))
			if structBody != "" {
				builder.WriteString(fmt.Sprintf("// 原始定义: %s\n", structBody))
			}

			camelName := pe.toCamelCase(structName)
			builder.WriteString(fmt.Sprintf("type %s struct {\n", camelName))

			// 根据结构类型生成字段
			switch strings.ToLower(structType) {
			case "array":
				builder.WriteString("\tLength   uint16      // 数组长度\n")
				builder.WriteString("\tElements interface{} // 数组元素\n")
			case "choice":
				builder.WriteString("\tTag  uint8       // 选择标识\n")
				builder.WriteString("\tData interface{} // 选择的数据\n")
			default:
				builder.WriteString("\t// TODO: 根据标准文档完善字段定义\n")
				if structBody != "" {
					builder.WriteString(fmt.Sprintf("\t// 参考: %s\n", structBody))
				}
			}

			builder.WriteString("}\n\n")
		}
	}

	filePath := filepath.Join(pe.outputDir, "axdr_structures_v3.go")
	return os.WriteFile(filePath, []byte(builder.String()), 0644)
}

// saveTables 保存表格数据
func (pe *PDFExtractorV3) saveTables() error {
	tables, ok := pe.structured["tables"].(map[string]interface{})
	if !ok {
		return nil
	}

	var builder strings.Builder
	builder.WriteString("package datatype\n\n")
	builder.WriteString("// A-XDR表格数据 (从DLT 790.6-2010标准提取 - V3版本)\n\n")

	for tableName, tableInfo := range tables {
		if info, ok := tableInfo.(map[string]interface{}); ok {
			title := info["title"].(string)
			content := info["content"].(string)

			builder.WriteString(fmt.Sprintf("// %s: %s\n", tableName, title))
			if content != "" {
				builder.WriteString(fmt.Sprintf("// 内容: %s\n", content))
			}
			builder.WriteString("\n")
		}
	}

	filePath := filepath.Join(pe.outputDir, "axdr_tables_v3.go")
	return os.WriteFile(filePath, []byte(builder.String()), 0644)
}

// mapToGoType 将A-XDR类型映射到Go类型
func (pe *PDFExtractorV3) mapToGoType(axdrType string) string {
	typeMap := map[string]string{
		// 基本类型
		"boolean":              "bool",
		"布尔":                   "bool",
		"bit-string":           "[]byte",
		"位串":                   "[]byte",
		"double-long":          "int32",
		"双长整数":                 "int32",
		"double-long-unsigned": "uint32",
		"双长无符号":                "uint32",
		"enum":                 "int",
		"枚举":                   "int",
		"float32":              "float32",
		"浮点32":                 "float32",
		"float64":              "float64",
		"浮点64":                 "float64",
		"integer":              "int8",
		"整数":                   "int8",
		"long":                 "int16",
		"长整数":                  "int16",
		"long-unsigned":        "uint16",
		"长无符号":                 "uint16",
		"long64":               "int64",
		"长64":                  "int64",
		"long64-unsigned":      "uint64",
		"长64无符号":               "uint64",
		"octet-string":         "[]byte",
		"八位字节串":                "[]byte",
		"unsigned":             "uint8",
		"无符号":                  "uint8",
		"visible-string":       "string",
		"可见字符串":                "string",
		"utf8-string":          "string",
		"UTF8字符串":              "string",
		"bcd":                  "[]byte",
		"BCD":                  "[]byte",
		"date":                 "[]byte",
		"日期":                   "[]byte",
		"time":                 "[]byte",
		"时间":                   "[]byte",
		"date-time":            "[]byte",
		"日期时间":                 "[]byte",
		"dont-care":            "interface{}",
		"无关":                   "interface{}",
		// 复合类型
		"array":     "[]interface{}",
		"数组":        "[]interface{}",
		"structure": "struct{}",
		"结构":        "struct{}",
		"choice":    "interface{}",
		"选择":        "interface{}",
	}

	return typeMap[strings.ToLower(axdrType)]
}

// toCamelCase 转换为驼峰命名
func (pe *PDFExtractorV3) toCamelCase(s string) string {
	// 处理中文和英文混合的情况
	s = strings.ReplaceAll(s, "布尔", "Boolean")
	s = strings.ReplaceAll(s, "位串", "BitString")
	s = strings.ReplaceAll(s, "双长整数", "DoubleLong")
	s = strings.ReplaceAll(s, "双长无符号", "DoubleLongUnsigned")
	s = strings.ReplaceAll(s, "枚举", "Enum")
	s = strings.ReplaceAll(s, "浮点32", "Float32")
	s = strings.ReplaceAll(s, "浮点64", "Float64")
	s = strings.ReplaceAll(s, "整数", "Integer")
	s = strings.ReplaceAll(s, "长整数", "Long")
	s = strings.ReplaceAll(s, "长无符号", "LongUnsigned")
	s = strings.ReplaceAll(s, "长64", "Long64")
	s = strings.ReplaceAll(s, "长64无符号", "Long64Unsigned")
	s = strings.ReplaceAll(s, "八位字节串", "OctetString")
	s = strings.ReplaceAll(s, "无符号", "Unsigned")
	s = strings.ReplaceAll(s, "可见字符串", "VisibleString")
	s = strings.ReplaceAll(s, "UTF8字符串", "UTF8String")
	s = strings.ReplaceAll(s, "日期", "Date")
	s = strings.ReplaceAll(s, "时间", "Time")
	s = strings.ReplaceAll(s, "日期时间", "DateTime")
	s = strings.ReplaceAll(s, "无关", "DontCare")
	s = strings.ReplaceAll(s, "数组", "Array")
	s = strings.ReplaceAll(s, "结构", "Structure")
	s = strings.ReplaceAll(s, "选择", "Choice")

	// 分割字符串
	parts := strings.FieldsFunc(s, func(r rune) bool {
		return r == '-' || r == '_' || r == ' ' || r == '.' || r == '/' || r == '\\'
	})

	// 转换为驼峰命名
	for i, part := range parts {
		if len(part) > 0 {
			parts[i] = strings.ToUpper(part[:1]) + strings.ToLower(part[1:])
		}
	}

	result := strings.Join(parts, "")

	// 确保首字母大写
	if len(result) > 0 {
		result = strings.ToUpper(result[:1]) + result[1:]
	}

	return result
}

// GetContent 获取提取的内容
func (pe *PDFExtractorV3) GetContent() string {
	return pe.content
}

// GetStructuredData 获取结构化数据
func (pe *PDFExtractorV3) GetStructuredData() map[string]interface{} {
	return pe.structured
}
