# DLT 69845 A-XDR 数据类型实现

基于 DL/T 790.6-2010《采用配电线载波的配电自动化 第6部分: A-XDR编码规则》标准的 Golang 实现。

## 项目结构

```
internal/protocol/dlt69845/datatype/
├── README.md                      # 本文档
├── manual_axdr_types.go          # A-XDR基本数据类型定义
├── axdr_codec.go                 # A-XDR编解码工具
├── axdr_encoding_rules.md        # A-XDR编码规则详细说明
├── structure.go                  # DLT69845协议帧结构定义
├── basic.go                      # 基础类型定义
├── pdf_extractor.go              # PDF内容提取工具 (v1)
├── pdf_extractor_v2.go           # PDF内容提取工具 (v2)
├── dlt790_6_raw_content.txt      # 提取的PDF原始内容
├── axdr_types.go                 # 自动生成的类型定义
├── axdr_encoding_rules.go        # 自动生成的编码规则
└── axdr_structures.go            # 自动生成的结构定义
```

## 核心功能

### 1. A-XDR 基本数据类型

支持 DL/T 790.6-2010 标准中定义的所有基本数据类型：

- **Boolean**: 布尔类型 (1字节)
- **Integer**: 8位有符号整数
- **Unsigned**: 8位无符号整数
- **Long**: 16位有符号整数
- **Long-Unsigned**: 16位无符号整数
- **Double-Long**: 32位有符号整数
- **Double-Long-Unsigned**: 32位无符号整数
- **Long64**: 64位有符号整数
- **Long64-Unsigned**: 64位无符号整数
- **Float32**: 32位浮点数 (IEEE 754)
- **Float64**: 64位浮点数 (IEEE 754)

### 2. 复合数据类型

- **Bit-String**: 位串
- **Octet-String**: 八位字节串
- **Visible-String**: 可见字符串 (ASCII)
- **UTF8-String**: UTF-8字符串
- **BCD**: 二进制编码十进制
- **Array**: 数组
- **Structure**: 结构
- **Choice**: 选择类型
- **Compact-Array**: 紧凑数组

### 3. 时间日期类型

- **Date**: 日期类型 (12字节)
- **Time**: 时间类型 (4字节)
- **Date-Time**: 日期时间类型

### 4. 编解码功能

提供完整的 A-XDR 编解码功能：

- 大端序编码
- 可变长度编码
- 类型安全的编解码接口
- 错误处理和验证

## 使用示例

### 基本类型编解码

```go
package main

import (
    "fmt"
    "tp.service/internal/protocol/dlt69845/datatype"
)

func main() {
    // 创建编解码工具
    codec := datatype.NewAXDRCodecUtil()
    
    // 编码布尔值
    encoded := codec.EncodeBoolean(true)
    fmt.Printf("编码结果: %02X\n", encoded) // 输出: 01
    
    // 解码布尔值
    decoded, err := codec.DecodeBoolean(encoded)
    if err != nil {
        panic(err)
    }
    fmt.Printf("解码结果: %v\n", decoded) // 输出: true
    
    // 编码16位整数
    longEncoded := codec.EncodeLong(-1000)
    fmt.Printf("Long编码: %02X\n", longEncoded) // 输出: FC18
    
    // 编码字符串
    strEncoded := codec.EncodeVisibleString("Hello")
    fmt.Printf("字符串编码: %02X\n", strEncoded) // 输出: 0548656C6C6F
}
```

### 日期时间处理

```go
// 创建日期
date := datatype.Date{
    Year:        2024,
    Month:       6,
    Day:         28,
    DayOfWeek:   5, // 星期五
    Hour:        14,
    Minute:      30,
    Second:      25,
    Hundredths:  50,
    Deviation:   480, // UTC+8
    ClockStatus: 0,
}

// 编码日期
encoded := codec.EncodeDate(date)
fmt.Printf("日期编码: %02X\n", encoded)

// 解码日期
decoded, err := codec.DecodeDate(encoded)
if err != nil {
    panic(err)
}
fmt.Printf("解码日期: %+v\n", decoded)
```

### BCD编码

```go
// 编码BCD
bcdData, err := codec.EncodeBCD("1234")
if err != nil {
    panic(err)
}
fmt.Printf("BCD编码: %02X\n", bcdData) // 输出: 1234

// 解码BCD
decoded := codec.DecodeBCD(bcdData)
fmt.Printf("BCD解码: %s\n", decoded) // 输出: 1234
```

### 长度编码

```go
// 编码长度
length := uint64(1000)
encoded := datatype.EncodeLength(length)
fmt.Printf("长度编码: %02X\n", encoded) // 输出: 8203E8

// 解码长度
decoded, consumed, err := datatype.DecodeLength(encoded)
if err != nil {
    panic(err)
}
fmt.Printf("长度解码: %d, 消耗字节: %d\n", decoded, consumed)
```

## 测试

运行测试程序验证所有功能：

```bash
# 运行A-XDR编解码测试
go run cmd/axdr_test/main.go

# 运行PDF提取工具
go run cmd/pdf_extractor/main.go
```

## 编码规则

### 字节序
- 所有多字节数据采用**大端序**（Big-Endian）编码

### 长度编码规则
- 长度 ≤ 127：使用1字节直接编码
- 长度 > 127：使用多字节编码
  - 第1字节：0x80 | 后续字节数
  - 后续字节：实际长度值（大端序）

### 示例
- 长度 50 → `32`
- 长度 128 → `81 80`
- 长度 1000 → `82 03 E8`

## 错误处理

定义了标准的错误类型：

```go
var (
    ErrInvalidData   = fmt.Errorf("无效数据")
    ErrDataTooShort  = fmt.Errorf("数据太短")
    ErrInvalidLength = fmt.Errorf("无效长度")
    ErrInvalidType   = fmt.Errorf("无效类型")
)
```

## 常量定义

提供了常用的A-XDR编码常量：

```go
const (
    // 布尔值常量
    BooleanFalse = 0x00
    BooleanTrue  = 0x01
    
    // 数组长度编码标识
    ArrayLengthByte   = 0x01 // 1字节长度
    ArrayLengthWord   = 0x02 // 2字节长度
    ArrayLengthDWord  = 0x04 // 4字节长度
    ArrayLengthQWord  = 0x08 // 8字节长度
    
    // 选择类型标识
    ChoiceTag0  = 0x00
    ChoiceTag1  = 0x01
    // ... 更多选择标识
    
    // 时钟状态位定义
    ClockStatusInvalid          = 0x01 // 无效时间
    ClockStatusDoubtful         = 0x02 // 可疑时间
    ClockStatusDifferentBase    = 0x04 // 不同的时间基准
    ClockStatusInvalidStatus    = 0x08 // 无效的状态
    ClockStatusDaylightSaving   = 0x80 // 夏令时
)
```

## 接口定义

提供了标准的编解码接口：

```go
// A-XDR编码规则接口
type AXDREncoder interface {
    Encode() ([]byte, error)
}

// A-XDR解码规则接口
type AXDRDecoder interface {
    Decode(data []byte) error
}

// A-XDR编解码器接口
type AXDRCodec interface {
    AXDREncoder
    AXDRDecoder
}
```

## 依赖项

- Go 1.24.2+
- 标准库：`encoding/binary`, `math`, `fmt`

## 参考标准

- DL/T 790.6-2010《采用配电线载波的配电自动化 第6部分: A-XDR编码规则》
- IEEE 754 浮点数标准
- UTF-8 编码标准
- ASCII 字符集标准

## 许可证

本项目遵循项目根目录的许可证条款。
