package datatype

import (
	"encoding/binary"
	"fmt"
	"math"
)

// AXDRCodecUtil A-XDR编解码工具类
type AXDRCodecUtil struct{}

// NewAXDRCodecUtil 创建新的A-XDR编解码工具
func NewAXDRCodecUtil() *AXDRCodecUtil {
	return &AXDRCodecUtil{}
}

// EncodeBoolean 编码布尔值
func (c *AXDRCodecUtil) EncodeBoolean(value bool) []byte {
	if value {
		return []byte{BooleanTrue}
	}
	return []byte{BooleanFalse}
}

// DecodeBoolean 解码布尔值
func (c *AXDRCodecUtil) DecodeBoolean(data []byte) (bool, error) {
	if len(data) < 1 {
		return false, ErrDataTooShort
	}
	switch data[0] {
	case BooleanTrue:
		return true, nil
	case BooleanFalse:
		return false, nil
	default:
		return false, ErrInvalidData
	}
}

// EncodeInteger 编码8位有符号整数
func (c *AXDRCodecUtil) EncodeInteger(value int8) []byte {
	return []byte{byte(value)}
}

// DecodeInteger 解码8位有符号整数
func (c *AXDRCodecUtil) DecodeInteger(data []byte) (int8, error) {
	if len(data) < 1 {
		return 0, ErrDataTooShort
	}
	return int8(data[0]), nil
}

// EncodeUnsigned 编码8位无符号整数
func (c *AXDRCodecUtil) EncodeUnsigned(value uint8) []byte {
	return []byte{value}
}

// DecodeUnsigned 解码8位无符号整数
func (c *AXDRCodecUtil) DecodeUnsigned(data []byte) (uint8, error) {
	if len(data) < 1 {
		return 0, ErrDataTooShort
	}
	return data[0], nil
}

// EncodeLong 编码16位有符号整数
func (c *AXDRCodecUtil) EncodeLong(value int16) []byte {
	result := make([]byte, 2)
	binary.BigEndian.PutUint16(result, uint16(value))
	return result
}

// DecodeLong 解码16位有符号整数
func (c *AXDRCodecUtil) DecodeLong(data []byte) (int16, error) {
	if len(data) < 2 {
		return 0, ErrDataTooShort
	}
	return int16(binary.BigEndian.Uint16(data)), nil
}

// EncodeLongUnsigned 编码16位无符号整数
func (c *AXDRCodecUtil) EncodeLongUnsigned(value uint16) []byte {
	result := make([]byte, 2)
	binary.BigEndian.PutUint16(result, value)
	return result
}

// DecodeLongUnsigned 解码16位无符号整数
func (c *AXDRCodecUtil) DecodeLongUnsigned(data []byte) (uint16, error) {
	if len(data) < 2 {
		return 0, ErrDataTooShort
	}
	return binary.BigEndian.Uint16(data), nil
}

// EncodeDoubleLong 编码32位有符号整数
func (c *AXDRCodecUtil) EncodeDoubleLong(value int32) []byte {
	result := make([]byte, 4)
	binary.BigEndian.PutUint32(result, uint32(value))
	return result
}

// DecodeDoubleLong 解码32位有符号整数
func (c *AXDRCodecUtil) DecodeDoubleLong(data []byte) (int32, error) {
	if len(data) < 4 {
		return 0, ErrDataTooShort
	}
	return int32(binary.BigEndian.Uint32(data)), nil
}

// EncodeDoubleLongUnsigned 编码32位无符号整数
func (c *AXDRCodecUtil) EncodeDoubleLongUnsigned(value uint32) []byte {
	result := make([]byte, 4)
	binary.BigEndian.PutUint32(result, value)
	return result
}

// DecodeDoubleLongUnsigned 解码32位无符号整数
func (c *AXDRCodecUtil) DecodeDoubleLongUnsigned(data []byte) (uint32, error) {
	if len(data) < 4 {
		return 0, ErrDataTooShort
	}
	return binary.BigEndian.Uint32(data), nil
}

// EncodeLong64 编码64位有符号整数
func (c *AXDRCodecUtil) EncodeLong64(value int64) []byte {
	result := make([]byte, 8)
	binary.BigEndian.PutUint64(result, uint64(value))
	return result
}

// DecodeLong64 解码64位有符号整数
func (c *AXDRCodecUtil) DecodeLong64(data []byte) (int64, error) {
	if len(data) < 8 {
		return 0, ErrDataTooShort
	}
	return int64(binary.BigEndian.Uint64(data)), nil
}

// EncodeLong64Unsigned 编码64位无符号整数
func (c *AXDRCodecUtil) EncodeLong64Unsigned(value uint64) []byte {
	result := make([]byte, 8)
	binary.BigEndian.PutUint64(result, value)
	return result
}

// DecodeLong64Unsigned 解码64位无符号整数
func (c *AXDRCodecUtil) DecodeLong64Unsigned(data []byte) (uint64, error) {
	if len(data) < 8 {
		return 0, ErrDataTooShort
	}
	return binary.BigEndian.Uint64(data), nil
}

// EncodeFloat32 编码32位浮点数
func (c *AXDRCodecUtil) EncodeFloat32(value float32) []byte {
	result := make([]byte, 4)
	binary.BigEndian.PutUint32(result, math.Float32bits(value))
	return result
}

// DecodeFloat32 解码32位浮点数
func (c *AXDRCodecUtil) DecodeFloat32(data []byte) (float32, error) {
	if len(data) < 4 {
		return 0, ErrDataTooShort
	}
	bits := binary.BigEndian.Uint32(data)
	return math.Float32frombits(bits), nil
}

// EncodeFloat64 编码64位浮点数
func (c *AXDRCodecUtil) EncodeFloat64(value float64) []byte {
	result := make([]byte, 8)
	binary.BigEndian.PutUint64(result, math.Float64bits(value))
	return result
}

// DecodeFloat64 解码64位浮点数
func (c *AXDRCodecUtil) DecodeFloat64(data []byte) (float64, error) {
	if len(data) < 8 {
		return 0, ErrDataTooShort
	}
	bits := binary.BigEndian.Uint64(data)
	return math.Float64frombits(bits), nil
}

// EncodeOctetString 编码八位字节串
func (c *AXDRCodecUtil) EncodeOctetString(value []byte) []byte {
	length := EncodeLength(uint64(len(value)))
	result := make([]byte, 0, len(length)+len(value))
	result = append(result, length...)
	result = append(result, value...)
	return result
}

// DecodeOctetString 解码八位字节串
func (c *AXDRCodecUtil) DecodeOctetString(data []byte) ([]byte, int, error) {
	length, consumed, err := DecodeLength(data)
	if err != nil {
		return nil, 0, err
	}
	
	if uint64(len(data)) < uint64(consumed)+length {
		return nil, 0, ErrDataTooShort
	}
	
	value := make([]byte, length)
	copy(value, data[consumed:consumed+int(length)])
	return value, consumed + int(length), nil
}

// EncodeVisibleString 编码可见字符串
func (c *AXDRCodecUtil) EncodeVisibleString(value string) []byte {
	data := []byte(value)
	length := EncodeLength(uint64(len(data)))
	result := make([]byte, 0, len(length)+len(data))
	result = append(result, length...)
	result = append(result, data...)
	return result
}

// DecodeVisibleString 解码可见字符串
func (c *AXDRCodecUtil) DecodeVisibleString(data []byte) (string, int, error) {
	length, consumed, err := DecodeLength(data)
	if err != nil {
		return "", 0, err
	}
	
	if uint64(len(data)) < uint64(consumed)+length {
		return "", 0, ErrDataTooShort
	}
	
	value := string(data[consumed : consumed+int(length)])
	return value, consumed + int(length), nil
}

// EncodeUTF8String 编码UTF-8字符串
func (c *AXDRCodecUtil) EncodeUTF8String(value string) []byte {
	data := []byte(value)
	length := EncodeLength(uint64(len(data)))
	result := make([]byte, 0, len(length)+len(data))
	result = append(result, length...)
	result = append(result, data...)
	return result
}

// DecodeUTF8String 解码UTF-8字符串
func (c *AXDRCodecUtil) DecodeUTF8String(data []byte) (string, int, error) {
	length, consumed, err := DecodeLength(data)
	if err != nil {
		return "", 0, err
	}
	
	if uint64(len(data)) < uint64(consumed)+length {
		return "", 0, ErrDataTooShort
	}
	
	value := string(data[consumed : consumed+int(length)])
	return value, consumed + int(length), nil
}

// EncodeBCD 编码BCD数据
func (c *AXDRCodecUtil) EncodeBCD(value string) ([]byte, error) {
	// 移除非数字字符
	digits := ""
	for _, r := range value {
		if r >= '0' && r <= '9' {
			digits += string(r)
		}
	}
	
	// 如果位数为奇数，前面补0
	if len(digits)%2 == 1 {
		digits = "0" + digits
	}
	
	result := make([]byte, len(digits)/2)
	for i := 0; i < len(digits); i += 2 {
		high := digits[i] - '0'
		low := digits[i+1] - '0'
		result[i/2] = byte(high<<4 | low)
	}
	
	return result, nil
}

// DecodeBCD 解码BCD数据
func (c *AXDRCodecUtil) DecodeBCD(data []byte) string {
	result := ""
	for _, b := range data {
		high := (b >> 4) & 0x0F
		low := b & 0x0F
		result += fmt.Sprintf("%d%d", high, low)
	}
	return result
}

// EncodeDate 编码日期
func (c *AXDRCodecUtil) EncodeDate(date Date) []byte {
	result := make([]byte, 12)
	binary.BigEndian.PutUint16(result[0:2], date.Year)
	result[2] = date.Month
	result[3] = date.Day
	result[4] = date.DayOfWeek
	result[5] = date.Hour
	result[6] = date.Minute
	result[7] = date.Second
	result[8] = date.Hundredths
	binary.BigEndian.PutUint16(result[9:11], uint16(date.Deviation))
	result[11] = date.ClockStatus
	return result
}

// DecodeDate 解码日期
func (c *AXDRCodecUtil) DecodeDate(data []byte) (Date, error) {
	if len(data) < 12 {
		return Date{}, ErrDataTooShort
	}
	
	return Date{
		Year:        binary.BigEndian.Uint16(data[0:2]),
		Month:       data[2],
		Day:         data[3],
		DayOfWeek:   data[4],
		Hour:        data[5],
		Minute:      data[6],
		Second:      data[7],
		Hundredths:  data[8],
		Deviation:   int16(binary.BigEndian.Uint16(data[9:11])),
		ClockStatus: data[11],
	}, nil
}

// EncodeTime 编码时间
func (c *AXDRCodecUtil) EncodeTime(time Time) []byte {
	return []byte{time.Hour, time.Minute, time.Second, time.Hundredths}
}

// DecodeTime 解码时间
func (c *AXDRCodecUtil) DecodeTime(data []byte) (Time, error) {
	if len(data) < 4 {
		return Time{}, ErrDataTooShort
	}
	
	return Time{
		Hour:       data[0],
		Minute:     data[1],
		Second:     data[2],
		Hundredths: data[3],
	}, nil
}

// EncodeEnum 编码枚举
func (c *AXDRCodecUtil) EncodeEnum(value uint8) []byte {
	return []byte{value}
}

// DecodeEnum 解码枚举
func (c *AXDRCodecUtil) DecodeEnum(data []byte) (uint8, error) {
	if len(data) < 1 {
		return 0, ErrDataTooShort
	}
	return data[0], nil
}
