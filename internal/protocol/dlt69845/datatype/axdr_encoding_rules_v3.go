package datatype

// A-XDR编码规则 (从DLT 790.6-2010标准提取 - V3版本)


// A-XDR编码规则常量
const (
	// 字节序
	BigEndian = true // A-XDR使用大端序

	// 长度编码阈值
	ShortLengthMax = 127 // 短格式长度最大值

	// 编码标识
	LongLengthFlag = 0x80 // 长格式长度标识
)

// IsShortLength 判断是否为短格式长度编码
func IsShortLength(length uint64) bool {
	return length <= ShortLengthMax
}

// GetLengthEncodingSize 获取长度编码所需字节数
func GetLengthEncodingSize(length uint64) int {
	if IsShortLength(length) {
		return 1
	}
	// 计算需要多少字节表示长度值
	bytes := 0
	temp := length
	for temp > 0 {
		bytes++
		temp >>= 8
	}
	return 1 + bytes // 1字节标识 + 长度字节
}
