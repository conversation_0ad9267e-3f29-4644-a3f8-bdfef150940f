package test

import (
	"testing"
	"time"

	"tp.service/internal/common"
	"tp.service/internal/logger"
)

func TestSerial(t *testing.T) {
	serialPort := common.NewSerial(&common.SerialConfig{
		Port:     "COM113",
		BaudRate: 9600,
		DataBits: common.DataBits8,
		StopBits: common.StopBits1,
		Parity:   common.ParityNone,
		Timeout:  5000 * time.Millisecond,
	})
	logger.Info("串口参数: %v", serialPort)

	if err := serialPort.Connect(); err != nil {
		t.Error(err)
		return
	}
	logger.Info("串口连接成功")

	tmp := []byte{0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A}
	if err := serialPort.Send(tmp, 3); err != nil {
		t.Error(err)
		return
	}
	logger.Info("发送数据成功")

	recv, err := serialPort.Receive(0)
	if err != nil {
		t.Error(err)
		return
	}
	logger.Info("接收数据成功: %v, String: %s", recv, string(recv))

	if recv, err = serialPort.SendAndReceive(tmp, 0); err != nil {
		t.Error(err)
		return
	}
	logger.Info("发送并接收数据成功: %v, String: %s", recv, string(recv))

	if err := serialPort.Disconnect(); err != nil {
		t.Error(err)
		return
	}

	logger.Info("串口断开成功")
}

func TestSerialReceiveStream(t *testing.T) {
	serialPort := common.NewSerial(&common.SerialConfig{
		Port:     "COM113",
		BaudRate: 9600,
		DataBits: common.DataBits8,
		StopBits: common.StopBits1,
		Parity:   common.ParityNone,
		Timeout:  5000 * time.Millisecond,
	})
	logger.Info("串口参数: %v", serialPort)

	if err := serialPort.Connect(); err != nil {
		t.Error(err)
		return
	}
	logger.Info("串口连接成功")

	logger.Info("开始接收数据")
	channel := make(chan []byte, 10) // 带缓冲的channel，避免阻塞

	// 启动数据接收goroutine
	go func() {
		if err := serialPort.ReceiveStream(channel); err != nil {
			logger.Error("ReceiveStream 错误: %v", err)
			t.Error(err)
			return
		}
		logger.Info("ReceiveStream 已停止")
	}()

	// 启动数据处理goroutine
	go func() {
		for data := range channel {
			logger.Info("接收到数据: %v, 字符串: %s, 长度: %d", data, string(data), len(data))
		}
		logger.Info("数据处理goroutine已退出")
	}()

	// 等待一段时间接收数据
	logger.Info("等待接收数据...")
	time.Sleep(10 * time.Second)

	if err := serialPort.StopReceive(); err != nil {
		t.Error(err)
		return
	}
	logger.Info("串口停止接收成功")

	// 等待一下让goroutine有时间退出
	time.Sleep(2 * time.Second)

	if err := serialPort.Disconnect(); err != nil {
		t.Error(err)
		return
	}
	logger.Info("串口断开成功")
}
